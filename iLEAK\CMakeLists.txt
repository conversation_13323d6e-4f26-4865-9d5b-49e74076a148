cmake_minimum_required(VERSION 3.2)

project(iLEAK)

# 开启所有Warning提示
add_compile_options(-Wall -Wextra)
set(CMAKE_CXX_FLAGS "-Wno-pmf-conversions")

# 指定编译器使用C/C++ 11
set(CMAKE_C_STANDARD_REQUIRED ON)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 11)

# 将指定目录添加到编译器的头文件搜索路径之下
include_directories(${PROJECT_SOURCE_DIR})
include_directories(${PROJECT_SOURCE_DIR}/BlueSocket/include)

# 指定引用的外部库的搜索路径
link_directories(${PROJECT_SOURCE_DIR}/BlueSocket/lib)

# 源文件集合
#set(SRC
#${PROJECT_SOURCE_DIR}/iLEAK.cpp
#${PROJECT_SOURCE_DIR}/main.cpp)
SET(SRC
  iLEAK.cpp
  iLEAK.h
  main.cpp
  iLEAK_Info.h
  iLEAK_Info.cpp
)


# 使用指定的源文件来生成目标可执行文件
add_executable(iLEAK ${SRC})

# 将目标文件与库文件进行链接
target_link_libraries(iLEAK
${MOOS_LIBRARIES}
mbutil
pthread
BlueSocket)
