/************************************************************/
/*    NAME: zhaoqinchao                                     */
/*    ORGN: HEU                                             */
/*    FILE: BlueSocket.cpp                                  */
/*    DATE: 2025/7/10                                       */
/*    VERSION: 2.1.0                                        */
/*    v2.0.0 (2025/7/10):                                   */
/*    - 添加网络连接重试机制                                    */
/*    - 实现 OpenSocketWithRetry() 方法                      */
/*    - 实现 RebindSocket() 方法                             */
/*    - 实现 CloseSocket() 私有方法                          */
/*    - 改进构造函数，正确初始化成员变量                         */
/*    - 改进 BindSocket() 方法，添加状态跟踪                   */
/*    - 增强错误处理和详细日志输出                              */
/************************************************************/

#include "BlueSocket.h"

using namespace std;

BlueSocket::BlueSocket()
{
    m_iSockfd = -1;
    m_bBindFlag = false;
}

BlueSocket::~BlueSocket()
{
    CloseSocket();
}

void BlueSocket::CloseSocket()
{
    if (m_iSockfd != -1)
    {
        close(m_iSockfd);
        m_iSockfd = -1;
        m_bBindFlag = false;
    }
}

bool BlueSocket::OpenSocket(const std::string &sIP, const int &iPort)
{
    // 如果已经有打开的socket，先关闭它以避免资源泄漏
    if (m_iSockfd != -1)
    {
        CloseSocket();
    }

    m_iSockfd = socket(AF_INET, SOCK_DGRAM, 0);
    memset(&m_SockAddr, 0, sizeof(struct sockaddr_in));
    m_SockAddr.sin_family = AF_INET;
    m_SockAddr.sin_addr.s_addr = inet_addr(sIP.c_str());
    m_SockAddr.sin_port = htons(iPort);

    if (m_iSockfd == -1)
    {
        printf("Cannot OpenSocket: ");
        printf("IP = %s, Port = %d \n", sIP.c_str(), iPort);
        printf("OpenSocket Error = %s \n", strerror(errno));
        m_bBindFlag = false;  // 确保绑定状态正确
        return false;
    }
    else
    {
        printf("OpenSocket: ");
        printf("Sockfd = %d, IP = %s, Port = %d \n", m_iSockfd, sIP.c_str(), iPort);
        m_bBindFlag = false;  // 新socket未绑定
        return true;
    }
}

bool BlueSocket::SetNonBlocking()
{
    int flag = fcntl(m_iSockfd, F_GETFL, 0);
    if (flag < 0)
    {
        printf("Cannot SetNonBlocking: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("SetNonBlocking Error = %s \n", strerror(errno));
        return false;
    }
    if (fcntl(m_iSockfd, F_SETFL, flag | O_NONBLOCK) < 0)
    {
        printf("Cannot SetNonBlocking: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("SetNonBlocking Error = %s \n", strerror(errno));
        return false;
    }

    printf("SetNonBlocking: ");
    printf("Sockfd = %d \n", m_iSockfd);

    return true;
}

bool BlueSocket::BindSocket()
{
    int ret = bind(m_iSockfd, (struct sockaddr *)&m_SockAddr, sizeof(struct sockaddr_in));
    if (ret < 0)
    {
        printf("Cannot SocketBind: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("SocketBind Error = %s \n", strerror(errno));

        // 使用统一的资源清理方法
        CloseSocket();
        return false;
    }
    else
    {
        printf("BindSocket: ");
        printf("Sockfd = %d \n", m_iSockfd);
        m_bBindFlag = true;
        return true;
    }
}

int BlueSocket::SendString(const std::string &data)
{
    ssize_t n = sendto(m_iSockfd, data.c_str(), data.size(), 0, (struct sockaddr *)&m_SockAddr, sizeof(struct sockaddr_in));

    if (n < 0)
    {
        printf("Cannot SocketSend: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("SocketSend Error = %s \n", strerror(errno));
    }

    return n;
}

int BlueSocket::SendBinary(const std::vector<uint8_t> &data)
{
    ssize_t n = sendto(m_iSockfd, data.data(), data.size(), 0, (struct sockaddr *)&m_SockAddr, sizeof(struct sockaddr_in));
//MOOSTrace("BlueSocket::SendBinary size_t = %d \n",n);

    if (n < 0)
    {
        printf("Cannot SocketSendHex: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("SocketSendHex Error = %s \n", strerror(errno));
    }

    return n;
}

int BlueSocket::RecvString(std::string &data, const int &iBufferSize)
{
    data.clear();
    data.shrink_to_fit();

    char ReadBuffer[iBufferSize + 1];
    memset(ReadBuffer, 0, sizeof(ReadBuffer));
    socklen_t addrlen = sizeof(struct sockaddr_in);
    int n = recvfrom(m_iSockfd, ReadBuffer, iBufferSize, 0, (struct sockaddr *)&m_SockAddr, &addrlen);

    if (n > 0)
    {
        data = string(ReadBuffer, n);
    }
    else
    {
        printf("Cannot RecvString: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("RecvString Error = %s \n", strerror(errno));
    }

    return n;
}

int BlueSocket::RecvBinary(std::vector<uint8_t> &data, const int &iBufferSize)
{
    data.clear();
    data.shrink_to_fit();

    uint8_t ReadBuffer[iBufferSize];
    memset(ReadBuffer, 0, sizeof(ReadBuffer));
    socklen_t addrlen = sizeof(struct sockaddr_in);
    int n = recvfrom(m_iSockfd, ReadBuffer, iBufferSize, 0, (struct sockaddr *)&m_SockAddr, &addrlen);

    if (n > 0)
    {
        data = vector<uint8_t>(ReadBuffer, ReadBuffer + n);
    }
    else
    {
        printf("Cannot RecvBinary: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("RecvBinary Error = %s \n", strerror(errno));
    }

    return n;
}

/**
 * @brief 打开UDP套接字并在失败时重试
 *
 * 如果初始连接尝试失败，该方法将进行多次重试，每次之间有指定的延迟
 * 在每次重试前会清理之前的socket资源，避免资源泄漏
 *
 * @param sIP          目标IP地址
 * @param iPort        目标端口号
 * @param maxRetries   最大重试次数
 * @param retryDelay   每次重试之间的延迟（秒）
 * @return bool        如果任何尝试成功则返回true，所有尝试都失败则返回false
 */
bool BlueSocket::OpenSocketWithRetry(const std::string &sIP, const int &iPort,
                                    const int maxRetries, const int retryDelay)
{
    // 第一次尝试连接
    bool connected = OpenSocket(sIP, iPort);
    if (connected) {
        printf("Socket connection established on first attempt: %s:%d\n", sIP.c_str(), iPort);
        return true;
    }

    // 如果第一次尝试失败，进行重试
    int retryCount = 0;
    while (!connected && retryCount < maxRetries) {
        retryCount++;
        printf("Connection failed. Retrying (%d/%d) after %d seconds...\n",
               retryCount, maxRetries, retryDelay);

        // 清理之前的socket资源
        CloseSocket();

        // 使用非阻塞延迟（现在会完成完整的延迟时间）
        if (!DelayWithTimeout(retryDelay)) {
            printf("Delay failed due to system error, continuing with retry\n");
        }

        // 重试连接
        connected = OpenSocket(sIP, iPort);
        if (connected) {
            printf("Socket connection established on retry %d: %s:%d\n",
                   retryCount, sIP.c_str(), iPort);
            return true;
        }
    }

    printf("Failed to establish socket connection after %d attempts to %s:%d\n",
           maxRetries + 1, sIP.c_str(), iPort);
    return false;
}

/**
 * @brief 重新绑定套接字
 *
 * 当绑定失败时尝试重新绑定套接字。对于某些绑定错误（如地址已被使用），
 * 会重新创建socket以确保绑定成功。使用非阻塞延迟避免线程阻塞。
 *
 * @param maxRetries    最大重试次数
 * @param retryDelay    每次重试之间的延迟（单位：秒）
 * @return bool         成功返回true，失败返回false
 */
bool BlueSocket::RebindSocket(const int maxRetries, const int retryDelay)
{
    // 确保套接字文件描述符有效
    if (m_iSockfd < 0) {
        printf("Invalid socket descriptor for rebinding\n");
        return false;
    }

    // 保存socket地址信息，因为重新创建socket时需要
    struct sockaddr_in savedAddr = m_SockAddr;

    // 第一次尝试绑定
    if (bind(m_iSockfd, (sockaddr *)&m_SockAddr, sizeof(sockaddr_in)) == 0) {
        printf("Socket rebind successful on first attempt (sockfd=%d)\n", m_iSockfd);
        m_bBindFlag = true;
        return true;
    }

    printf("Initial bind failed: %s\n", strerror(errno));

    // 如果第一次尝试失败，进行重试
    int retryCount = 0;
    while (retryCount < maxRetries) {
        retryCount++;
        printf("Rebind failed. Retrying (%d/%d) after %d seconds...\n",
               retryCount, maxRetries, retryDelay);

        // 对于某些错误（如EADDRINUSE），重新创建socket可能更有效
        int currentErrno = errno;
        if (currentErrno == EADDRINUSE || currentErrno == EADDRNOTAVAIL) {
            printf("Recreating socket due to address error\n");

            // 关闭当前socket
            CloseSocket();

            // 重新创建socket
            m_iSockfd = socket(AF_INET, SOCK_DGRAM, 0);
            if (m_iSockfd == -1) {
                printf("Failed to recreate socket: %s\n", strerror(errno));
                continue;
            }

            // 恢复地址信息
            m_SockAddr = savedAddr;
        }

        // 使用非阻塞延迟（现在会完成完整的延迟时间）
        if (!DelayWithTimeout(retryDelay)) {
            printf("Delay failed due to system error, continuing with retry\n");
        }

        // 重试绑定
        if (bind(m_iSockfd, (sockaddr *)&m_SockAddr, sizeof(sockaddr_in)) == 0) {
            printf("Socket rebind successful on retry %d (sockfd=%d)\n",
                   retryCount, m_iSockfd);
            m_bBindFlag = true;
            return true;
        }

        printf("Retry %d failed: %s\n", retryCount, strerror(errno));
    }

    printf("Failed to rebind socket after %d attempts (sockfd=%d)\n",
           maxRetries + 1, m_iSockfd);
    return false;
}

/**
 * @brief 非阻塞延迟方法
 *
 * 使用select()实现非阻塞延迟，相比sleep()更适合事件驱动程序。
 * 能够处理信号中断，确保完成完整的延迟时间。
 *
 * @param delaySeconds  延迟时间（秒）
 * @return bool         true=正常完成延迟，false=发生不可恢复的错误
 */
bool BlueSocket::DelayWithTimeout(const int delaySeconds)
{
    if (delaySeconds <= 0) {
        return true;
    }

    // 记录开始时间，用于计算剩余延迟时间
    struct timeval startTime, currentTime;
    gettimeofday(&startTime, NULL);

    struct timeval timeout;
    timeout.tv_sec = delaySeconds;
    timeout.tv_usec = 0;

    while (true) {
        // 使用select()进行延迟，不监听任何文件描述符
        int result = select(0, NULL, NULL, NULL, &timeout);

        if (result == 0) {
            // 超时正常结束 - 延迟完成
            printf("DelayWithTimeout completed successfully (%d seconds)\n", delaySeconds);
            return true;
        }
        else if (result < 0) {
            if (errno == EINTR) {
                // 被信号中断 - 计算剩余时间并继续
                gettimeofday(&currentTime, NULL);

                // 计算已经过去的时间（微秒）
                long elapsedMicros = (currentTime.tv_sec - startTime.tv_sec) * 1000000 +
                                   (currentTime.tv_usec - startTime.tv_usec);

                // 计算剩余时间（微秒）
                long totalMicros = delaySeconds * 1000000;
                long remainingMicros = totalMicros - elapsedMicros;

                if (remainingMicros <= 0) {
                    // 已经等待了足够长的时间
                    printf("DelayWithTimeout completed after signal interruption\n");
                    return true;
                }

                // 设置剩余的超时时间
                timeout.tv_sec = remainingMicros / 1000000;
                timeout.tv_usec = remainingMicros % 1000000;

                printf("DelayWithTimeout interrupted by signal, continuing with %ld.%06ld seconds remaining\n",
                       timeout.tv_sec, timeout.tv_usec);

                // 继续循环，使用剩余时间
                continue;
            }
            else {
                // 其他错误（非EINTR）
                printf("DelayWithTimeout failed: %s\n", strerror(errno));
                return false;
            }
        }

        // 不应该到达这里（result > 0对于我们的用例不可能）
        printf("DelayWithTimeout: unexpected select result %d\n", result);
        return true;
    }
}
