/************************************************************/
/*    NAME: zhaoqinchao                                     */
/*    ORGN: HEU                                             */
/*    FILE: BlueSocket.h                                    */
/*    DATE: 2025/7/10                                       */
/*    VERSION: 2.1.0                                        */
/*    v2.0.0 (2025/7/10):                                   */
/*    - 添加网络连接重试机制                                    */
/*    - 新增 OpenSocketWithRetry() 方法                      */
/*    - 新增 RebindSocket() 方法                             */
/*    - 新增 m_bBindFlag 成员变量跟踪绑定状态                   */
/*    - 新增 CloseSocket() 私有方法统一资源管理                 */
/*    - 改进错误处理和日志输出                                  */
/*    - 新增DelayWithTimeout()非阻塞延迟方法                  */
/*    - 改进信号中断后的时间计算和重试逻辑                       */
/************************************************************/

#ifndef _BLUESOCKET_H_
#define _BLUESOCKET_H_

#include <stdio.h>
#include <errno.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <string>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <vector>
#include <stdint.h>
#include <sys/select.h>
#include <sys/time.h>

class BlueSocket
{
public:
    BlueSocket();
    virtual ~BlueSocket();

protected:
    int m_iSockfd;
    struct sockaddr_in m_SockAddr;
    bool m_bBindFlag;

public:
    bool OpenSocket(const std::string &sIP, const int &iPort);
    bool OpenSocketWithRetry(const std::string &sIP, const int &iPort,
                            const int maxRetries, const int retryDelay);
    bool SetNonBlocking();
    bool BindSocket();
    bool RebindSocket(const int maxRetries, const int retryDelay);
    int SendString(const std::string &data);
    int SendBinary(const std::vector<uint8_t> &data);
    int RecvString(std::string &data, const int &iBufferSize);
    int RecvBinary(std::vector<uint8_t> &data, const int &iBufferSize);

private:
    void CloseSocket();
    bool DelayWithTimeout(const int delaySeconds);
};

#endif
