#include "main.h"
 
 
#define MS5637_WriteAddr 0xee //0xec
#define MS5637_ReadAddr 0xef
 
#define MS5637_CMD_RES 0x1E//
 
#define MS5637_CMD_PresConv_256 0x40//??ADC??
#define MS5637_CMD_TempConv_256 0x50//??ADC??
#define MS5637_CMD_PresConv_4096 0x48//??ADC??
#define MS5637_CMD_TempConv_4096 0x58//??ADC??
 
#define MS5637_C1_REG_H 0xA2
#define MS5637_C2_REG_H 0xA4
#define MS5637_C3_REG_H 0xA6
#define MS5637_C4_REG_H 0xA8
#define MS5637_C5_REG_H 0xAA
#define MS5637_C6_REG_H 0xAC
#define MS5637_data_REG_H 0x00
 
 
extern uint16_t C1,C2,C3,C4,C5,C6;
extern uint32_t D1,D2;
extern float cTemp,fTemp,pressure;
 
void MS5637_INIT(void);
void MS5637_Calculate_Val(void);
void GetPressandTemp(void);

