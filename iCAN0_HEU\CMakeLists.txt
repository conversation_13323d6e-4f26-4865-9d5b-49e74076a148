#--------------------------------------------------------
# The CMakeLists.txt for:                 pSpeedControl_HEU 
# Author(s):                              DZQ
#--------------------------------------------------------

# FILE(GLOB SRC *.cpp)
if (${WIN32})
  SET(SYSTEM_LIBS
    wsock32)
else (${WIN32})
  SET(SYSTEM_LIBS
    m
    pthread)
endif (${WIN32})

SET(SRC
  main.cpp
  CAN_HEU_Info.h
CAN_HEU_Info.cpp
  CAN_HEU.h
CAN_HEU.cpp
libsocketcan.c
can_netlink.h
libsocketcan.h
)



ADD_EXECUTABLE(iCAN0_HEU  ${SRC})

TARGET_LINK_LIBRARIES(iCAN0_HEU
   ${MOOS_LIBRARIES}
   mbutil
   m
   pthread
   geometry 
  ${SYSTEM_LIBS})

