#include "main.h"
#include "MS5611.h"
#include "i2c.h"
 
uint8_t TBuff[1];
uint8_t RBuff[3];
 
float cTemp,fTemp,pressure,depth;
uint16_t C1,C2,C3,C4,C5,C6;
uint32_t D1,D2;
 
uint8_t HALIIC_ReadMultByteFromSlave(uint8_t dev, uint8_t reg, uint8_t length, uint8_t *data)
{
  return HAL_I2C_Mem_Read(&hi2c2, dev, reg, I2C_MEMADD_SIZE_8BIT, data, length, 200);
}
 
void MS5637_INIT(void)
{

  TBuff[0]=MS5637_CMD_RES;
  HAL_I2C_Master_Transmit(&hi2c2,MS5637_WriteAddr,TBuff,1,100);
 
  HAL_Delay(100);
 
  HALIIC_ReadMultByteFromSlave(MS5637_ReadAddr,MS5637_C1_REG_H,2,RBuff);
  C1 = RBuff[0] * 256 + RBuff[1];

  HALIIC_ReadMultByteFromSlave(MS5637_ReadAddr,MS5637_C2_REG_H,2,RBuff);
  C2 = RBuff[0] * 256 + RBuff[1];
 
  HALIIC_ReadMultByteFromSlave(MS5637_ReadAddr,MS5637_C3_REG_H,2,RBuff);
  C3 = RBuff[0] * 256 + RBuff[1];

  HALIIC_ReadMultByteFromSlave(MS5637_ReadAddr,MS5637_C4_REG_H,2,RBuff);
  C4 = RBuff[0] * 256 + RBuff[1];

  HALIIC_ReadMultByteFromSlave(MS5637_ReadAddr,MS5637_C5_REG_H,2,RBuff);
  C5 = RBuff[0] * 256 + RBuff[1];
 
  HALIIC_ReadMultByteFromSlave(MS5637_ReadAddr,MS5637_C6_REG_H,2,RBuff);
  C6 = RBuff[0] * 256 + RBuff[1];
}
 
void GetPressandTemp(void)
{

  TBuff[0]=MS5637_CMD_RES;
  HAL_I2C_Master_Transmit(&hi2c2,MS5637_WriteAddr,TBuff,1,100);
 
  HAL_Delay(100);

  TBuff[0]=MS5637_CMD_PresConv_256;
  HAL_I2C_Master_Transmit(&hi2c2,MS5637_WriteAddr,TBuff,1,100);
 
  HAL_Delay(100);
 

  HALIIC_ReadMultByteFromSlave(MS5637_ReadAddr,MS5637_data_REG_H,3,RBuff);
  D1 = RBuff[0] * 65536 + RBuff[1] * 256 + RBuff[2];
 

  TBuff[0]=MS5637_CMD_TempConv_256;
  HAL_I2C_Master_Transmit(&hi2c2,MS5637_WriteAddr,TBuff,1,100);
 
  HAL_Delay(100);
 
  HALIIC_ReadMultByteFromSlave(MS5637_ReadAddr,MS5637_data_REG_H,3,RBuff);
  D2 = RBuff[0] * 65536 + RBuff[1] * 256 + RBuff[2];
}
 
void MS5637_Calculate_Val(void)
{
  int32_t dT;
  //float TEMP,T2,TEMP2;
	int32_t TEMP,T2,TEMP2;
  int64_t OFF,SENS,OFF2,SENS2,OFF3,SENS3;
 
  dT = D2 - C5 * 256;
  //TEMP = 2000 + (float)dT / 2048 * ((float)C6 / 4096);
	TEMP = 2000 + (float)dT*(float)C6 / 8388608;
	OFF =(int64_t)C2 * 65536 +  (int64_t)((double)C4 * dT )/128;
  //OFF =(int64_t)C2 * 131072 +  (int64_t)((double)C4 / 64 * dT );
  SENS =(int64_t)C1 * 32768 + (int64_t)((double)C3 * dT )/256;
	//SENS =(int64_t)C1 * 65536 + (int64_t)((double)C3 / 128 * dT );
  T2 = 0;
  OFF2 = 0;
  SENS2 = 0;
 
  if (TEMP > 2000)
    {

  	T2 = 0;
  	OFF2 = 0;
  	SENS2 = 0;
    }
 if (TEMP < 2000 )
    {
	  
  	T2 = (dT*dT)/2147483648;
  	
  	OFF2=(5*(TEMP-2000)*(TEMP-2000))/2 ;
		SENS2=(5*(TEMP-2000)*(TEMP-2000))/4;
			
  	if (TEMP < -1500)
  	  {
  		OFF2 = OFF2 + 7 * ((float)(TEMP + 1500) * (TEMP + 1500));
  		SENS2 = SENS2 + 11 * (((float)(TEMP + 1500) * (TEMP +1500))/2);
  	  }
    }
  
  OFF3 = OFF - OFF2;
  SENS3 = SENS - SENS2;
	TEMP2 = TEMP - T2;
	//pressure = ((float)((((double)(D1 * SENS3) / 2097152) - OFF3) / 8192)/10);
   //pressure = (((float)((((double)(D1 * SENS3) / 2097152) - OFF3) / 8192)/10)*100)/9800;
   pressure = ((float)(((double)(D1 * SENS3) / 2097152) - OFF3)/32768);
		cTemp=TEMP2;

}
