#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
CAN.BS1=CAN_BS1_2TQ
CAN.CalculateBaudRate=125000
CAN.CalculateTimeBit=8000
CAN.CalculateTimeQuantum=2000.0
CAN.IPParameters=CalculateTimeQuantum,CalculateTimeBit,CalculateBaudRate,BS1
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F103C8T6
Mcu.Family=STM32F1
Mcu.IP0=CAN
Mcu.IP1=I2C1
Mcu.IP2=I2C2
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=USART1
Mcu.IPNb=7
Mcu.Name=STM32F103C(8-B)Tx
Mcu.Package=LQFP48
Mcu.Pin0=PC13-TAMPER-RTC
Mcu.Pin1=PA0-WKUP
Mcu.Pin10=PB1
Mcu.Pin11=PB2
Mcu.Pin12=PB10
Mcu.Pin13=PB11
Mcu.Pin14=PB12
Mcu.Pin15=PB13
Mcu.Pin16=PB14
Mcu.Pin17=PB15
Mcu.Pin18=PA8
Mcu.Pin19=PA9
Mcu.Pin2=PA1
Mcu.Pin20=PA10
Mcu.Pin21=PA11
Mcu.Pin22=PA12
Mcu.Pin23=PA13
Mcu.Pin24=PA14
Mcu.Pin25=PA15
Mcu.Pin26=PB3
Mcu.Pin27=PB4
Mcu.Pin28=PB5
Mcu.Pin29=PB6
Mcu.Pin3=PA2
Mcu.Pin30=PB7
Mcu.Pin31=PB8
Mcu.Pin32=PB9
Mcu.Pin33=VP_SYS_VS_Systick
Mcu.Pin4=PA3
Mcu.Pin5=PA4
Mcu.Pin6=PA5
Mcu.Pin7=PA6
Mcu.Pin8=PA7
Mcu.Pin9=PB0
Mcu.PinsNb=34
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103C8Tx
MxCube.Version=6.7.0
MxDb.Version=DB.6.0.70
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.I2C1_ER_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.I2C1_EV_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.I2C2_ER_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.I2C2_EV_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.Locked=true
PA0-WKUP.Signal=GPIO_Input
PA1.Locked=true
PA1.Signal=GPIO_Input
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.Mode=CAN_Activate
PA11.Signal=CAN_RX
PA12.Mode=CAN_Activate
PA12.Signal=CAN_TX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA15.GPIOParameters=GPIO_PuPd
PA15.GPIO_PuPd=GPIO_NOPULL
PA15.Locked=true
PA15.Signal=GPIO_Output
PA2.Locked=true
PA2.Signal=GPIO_Input
PA3.Locked=true
PA3.Signal=GPIO_Input
PA4.Locked=true
PA4.Signal=GPIO_Input
PA5.Locked=true
PA5.Signal=GPIO_Input
PA6.GPIOParameters=GPIO_PuPd
PA6.GPIO_PuPd=GPIO_NOPULL
PA6.Locked=true
PA6.Signal=GPIO_Output
PA7.GPIOParameters=GPIO_PuPd
PA7.GPIO_PuPd=GPIO_NOPULL
PA7.Locked=true
PA7.Signal=GPIO_Output
PA8.GPIOParameters=GPIO_PuPd
PA8.GPIO_PuPd=GPIO_NOPULL
PA8.Locked=true
PA8.Signal=GPIO_Output
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.GPIOParameters=GPIO_PuPd
PB0.GPIO_PuPd=GPIO_NOPULL
PB0.Locked=true
PB0.Signal=GPIO_Output
PB1.GPIOParameters=GPIO_PuPd
PB1.GPIO_PuPd=GPIO_NOPULL
PB1.Locked=true
PB1.Signal=GPIO_Output
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB12.GPIOParameters=GPIO_PuPd
PB12.GPIO_PuPd=GPIO_NOPULL
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.GPIOParameters=GPIO_PuPd
PB13.GPIO_PuPd=GPIO_NOPULL
PB13.Locked=true
PB13.Signal=GPIO_Output
PB14.GPIOParameters=GPIO_PuPd
PB14.GPIO_PuPd=GPIO_NOPULL
PB14.Locked=true
PB14.Signal=GPIO_Output
PB15.GPIOParameters=GPIO_PuPd
PB15.GPIO_PuPd=GPIO_NOPULL
PB15.Locked=true
PB15.Signal=GPIO_Output
PB2.GPIOParameters=PinState,GPIO_PuPd
PB2.GPIO_PuPd=GPIO_NOPULL
PB2.Locked=true
PB2.PinState=GPIO_PIN_RESET
PB2.Signal=GPIO_Output
PB3.GPIOParameters=GPIO_PuPd
PB3.GPIO_PuPd=GPIO_NOPULL
PB3.Locked=true
PB3.Signal=GPIO_Output
PB4.Locked=true
PB4.Signal=GPIO_Output
PB5.Locked=true
PB5.Signal=GPIO_Output
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PB8.Locked=true
PB8.Signal=GPIO_Output
PB9.Locked=true
PB9.Signal=GPIO_Output
PC13-TAMPER-RTC.Locked=true
PC13-TAMPER-RTC.Signal=GPIO_Output
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103C8Tx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.5
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=20230925.ioc
ProjectManager.ProjectName=20230925
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_CAN_Init-CAN-false-HAL-true,4-MX_I2C1_Init-I2C1-false-HAL-true,5-MX_I2C2_Init-I2C2-false-HAL-true,6-MX_USART1_UART_Init-USART1-false-HAL-true
RCC.ADCFreqValue=4000000
RCC.AHBFreq_Value=8000000
RCC.APB1Freq_Value=8000000
RCC.APB1TimFreq_Value=8000000
RCC.APB2Freq_Value=8000000
RCC.APB2TimFreq_Value=8000000
RCC.FCLKCortexFreq_Value=8000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=8000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,SYSCLKFreq_VALUE,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=8000000
RCC.PLLCLKFreq_Value=16000000
RCC.PLLMCOFreq_Value=8000000
RCC.PLLMUL=RCC_PLL_MUL4
RCC.SYSCLKFreq_VALUE=8000000
RCC.TimSysFreq_Value=8000000
RCC.USBFreq_Value=16000000
RCC.VCOOutput2Freq_Value=4000000
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=custom
