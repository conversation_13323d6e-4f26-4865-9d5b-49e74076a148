# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket/build

# Include any dependencies generated for this target.
include CMakeFiles/BlueSocket.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/BlueSocket.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/BlueSocket.dir/flags.make

CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o: CMakeFiles/BlueSocket.dir/flags.make
CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o: ../src/BlueSocket.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o"
	/usr/bin/c++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o -c /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket/src/BlueSocket.cpp

CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.i"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket/src/BlueSocket.cpp > CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.i

CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.s"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket/src/BlueSocket.cpp -o CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.s

CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o.requires:

.PHONY : CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o.requires

CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o.provides: CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o.requires
	$(MAKE) -f CMakeFiles/BlueSocket.dir/build.make CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o.provides.build
.PHONY : CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o.provides

CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o.provides.build: CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o


# Object files for target BlueSocket
BlueSocket_OBJECTS = \
"CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o"

# External object files for target BlueSocket
BlueSocket_EXTERNAL_OBJECTS =

../lib/libBlueSocket.a: CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o
../lib/libBlueSocket.a: CMakeFiles/BlueSocket.dir/build.make
../lib/libBlueSocket.a: CMakeFiles/BlueSocket.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library ../lib/libBlueSocket.a"
	$(CMAKE_COMMAND) -P CMakeFiles/BlueSocket.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/BlueSocket.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/BlueSocket.dir/build: ../lib/libBlueSocket.a

.PHONY : CMakeFiles/BlueSocket.dir/build

CMakeFiles/BlueSocket.dir/requires: CMakeFiles/BlueSocket.dir/src/BlueSocket.cpp.o.requires

.PHONY : CMakeFiles/BlueSocket.dir/requires

CMakeFiles/BlueSocket.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/BlueSocket.dir/cmake_clean.cmake
.PHONY : CMakeFiles/BlueSocket.dir/clean

CMakeFiles/BlueSocket.dir/depend:
	cd /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket/build /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket/build /home/<USER>/moos-ivp-extend/src/iJLUBoard_HEU/BlueSocket/build/CMakeFiles/BlueSocket.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/BlueSocket.dir/depend

