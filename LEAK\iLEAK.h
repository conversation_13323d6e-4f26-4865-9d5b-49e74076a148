/************************************************************/
/*    NAME: zhaoqinchao                                    */
/*    ORGN: HEU                                             */
/*    FILE: iLEAK.h                                        */
/*    DATE: 2025/08/05  
/* 版本: 1.1.0
   日期: 2023/11/xx
   更新:
   - 修改HandleControlMessage函数中的设备映射
   - 调整SendControlFrame中的数据组合逻辑
   - 更新ParseControlFrame中的解析逻辑
/************************************************************/

#ifndef iLEAK_HEADER
#define iLEAK_HEADER

#include "MOOS/libMOOS/MOOSLib.h"
#include "MBUtils.h"
#include "BlueSocket.h"

const size_t FRAME_LEN = 13;

class iLEAK : public CMOOSApp
{
public:
  iLEAK();
  ~iLEAK();
  bool OnNewMail(MOOSMSG_LIST &NewMail);
  bool Iterate();
  bool OnConnectToServer();
  bool OnStartUp();
  void RegisterVariables();
  void RecvFrame();

private:             // Configuration variables
  int m_max_retries; // 最大重试次数
  int m_retry_delay; // 重试延迟（秒）

  void ParseFrame(std::vector<uint8_t> Frame);
  void ParseLeakageFrame(std::vector<uint8_t> Frame);
  void ParseControlFrame(std::vector<uint8_t> Frame);  // 解析设备控制帧
  void PublishCombinedLeakStatus();
  void SendFrame(std::vector<uint8_t> Frame);
  void SendControlFrame();  // 发送设备控制帧
  void HandleControlMessage(std::string msg);  // 处理CONTROL_MSG
  void HandleLoadAction(bool action);  // 处理LOAD_ACTION
  static void* RecvFrameWrapper(void* arg);  // 线程包装函数声明
  class BlueSocket RecvSock;
  class BlueSocket SendSock;

private: // State variables
  unsigned int m_iterations;
  double m_timewarp;

  // 线程控制变量
  bool m_thread_running;   // 线程运行标志
  pthread_t m_recv_thread; // 接收线程句柄

  // 漏水检测相关变量
  bool m_leak_detected;          // 总体漏水状态
  bool m_sensor_status[6];       // 6个传感器状态 (true=漏水, false=正常)
  uint8_t m_leak_checksum;       // 接收到的校验和
  uint8_t m_calculated_checksum; // 计算得出的校验和

  // LEAK1变量相关 - 来自iThruster_HEU模块的尾端漏水检测
  double m_leak1_status;         // LEAK1变量值 (0=正常, 64=尾端漏水故障)

  // 设备控制相关变量
  bool m_load_action;            // LOAD_ACTION变量值 (true=开启, false=关闭)
  std::string m_control_msg;     // CONTROL_MSG变量值
  uint8_t m_device_control[10];  // 设备控制数组，对应iCAN0_HEU的data1数组
  bool m_control_frame_ready;    // 控制帧准备发送标志
};

#endif
