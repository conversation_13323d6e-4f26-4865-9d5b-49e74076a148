T39A4 000:078.589   SEGGER J-Link V7.86d Log File
T39A4 000:078.747   DLL Compiled: Mar 15 2023 13:51:13
T39A4 000:078.761   Logging started @ 2024-01-15 03:45
T39A4 000:078.768 - 78.771ms
T39A4 000:078.780 JLINK_SetWarnOutHandler(...)
T39A4 000:078.789 - 0.011ms
T39A4 000:078.797 JLINK_OpenEx(...)
T39A4 000:079.934   Firmware: J-Link ARM-OB STM32 compiled Aug 22 2012 19:52:04
T39A4 000:080.303   Firmware: J-Link ARM-OB STM32 compiled Aug 22 2012 19:52:04
T39A4 000:082.188   Hardware: V7.00
T39A4 000:082.205   S/N: 20090929
T39A4 000:082.214   OEM: SEGGER
T39A4 000:082.224   Feature(s): RDI,FlashDL,<PERSON>B<PERSON>,<PERSON><PERSON><PERSON>,GDBFull
T39A4 000:083.152   TELNET listener socket opened on port 19021
T39A4 000:083.299   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T39A4 000:083.391   WEBSRV Webserver running on local port 19080
T39A4 000:088.747 - 9.962ms returns "O.K."
T39A4 000:088.778 JLINK_SetErrorOutHandler(...)
T39A4 000:088.785 - 0.010ms
T39A4 000:088.805 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\2023-12-19\MDK-ARM\JLinkSettings.ini"", ...). 
T39A4 000:100.396   Ref file found at: C:\Keil_v5\ARM\Segger\JLinkDevices.ref
T39A4 000:100.487   REF file references invalid XML file: B:\J-LINK\JLink\JLinkDevices.xml
T39A4 000:100.650 - 11.850ms returns 0x00
T39A4 000:102.850 JLINK_ExecCommand("Device = STM32F103C8", ...). 
T39A4 000:104.967   Device "STM32F103C8" selected.
T39A4 000:105.221 - 2.361ms returns 0x00
T39A4 000:105.235 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T39A4 000:105.246 - 0.005ms returns 0x01
T39A4 000:105.254 JLINK_GetHardwareVersion()
T39A4 000:105.261 - 0.009ms returns 70000
T39A4 000:105.272 JLINK_GetDLLVersion()
T39A4 000:105.279 - 0.009ms returns 78604
T39A4 000:105.287 JLINK_GetOEMString(...)
T39A4 000:105.297 JLINK_GetFirmwareString(...)
T39A4 000:105.305 - 0.011ms
T39A4 000:111.615 JLINK_GetDLLVersion()
T39A4 000:111.640 - 0.031ms returns 78604
T39A4 000:111.658 JLINK_GetCompileDateTime()
T39A4 000:111.666 - 0.011ms
T39A4 000:113.764 JLINK_GetFirmwareString(...)
T39A4 000:113.778 - 0.017ms
T39A4 000:115.622 JLINK_GetHardwareVersion()
T39A4 000:115.634 - 0.019ms returns 70000
T39A4 000:117.630 JLINK_GetSN()
T39A4 000:117.644 - 0.017ms returns 20090929
T39A4 000:119.440 JLINK_GetOEMString(...)
T39A4 000:123.486 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T39A4 000:124.101 - 0.623ms returns 0x00
T39A4 000:124.117 JLINK_HasError()
T39A4 000:124.136 JLINK_SetSpeed(20000)
T39A4 000:124.288 - 0.161ms
T39A4 000:124.305 JLINK_GetId()
T39A4 000:126.374   InitTarget() start
T39A4 000:126.392    J-Link Script File: Executing InitTarget()
T39A4 000:130.441   SWD selected. Executing JTAG -> SWD switching sequence.
T39A4 000:136.663   DAP initialized successfully.
T39A4 000:151.626   InitTarget() end - Took 22.3ms
T39A4 000:154.726   Found SW-DP with ID 0x1BA01477
T39A4 000:159.332   Old FW that does not support reading DPIDR via DAP jobs
T39A4 000:167.041   DPv0 detected
T39A4 000:170.112   CoreSight SoC-400 or earlier
T39A4 000:172.703   Scanning AP map to find all available APs
T39A4 000:177.597   AP[1]: Stopped AP scan as end of AP map has been reached
T39A4 000:179.793   AP[0]: AHB-AP (IDR: 0x14770011)
T39A4 000:183.302   Iterating through AP map to find AHB-AP to use
T39A4 000:191.785   AP[0]: Core found
T39A4 000:194.064   AP[0]: AHB-AP ROM base: 0xE00FF000
T39A4 000:198.418   CPUID register: 0x411FC231. Implementer code: 0x41 (ARM)
T39A4 000:200.611   Found Cortex-M3 r1p1, Little endian.
T39A4 000:200.908   -- Max. mem block: 0x00002C18
T39A4 000:201.357   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T39A4 000:201.809   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:204.782   FPUnit: 6 code (BP) slots and 2 literal slots
T39A4 000:204.800   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T39A4 000:205.261   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T39A4 000:205.846   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:206.316   CPU_WriteMem(4 bytes @ 0x********)
T39A4 000:206.828   CPU_ReadMem(4 bytes @ 0xE000ED88)
T39A4 000:207.289   CPU_WriteMem(4 bytes @ 0xE000ED88)
T39A4 000:207.813   CPU_ReadMem(4 bytes @ 0xE000ED88)
T39A4 000:208.287   CPU_WriteMem(4 bytes @ 0xE000ED88)
T39A4 000:212.904   CoreSight components:
T39A4 000:215.118   ROMTbl[0] @ E00FF000
T39A4 000:215.140   CPU_ReadMem(64 bytes @ 0xE00FF000)
T39A4 000:216.243   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T39A4 000:220.135   [0][0]: E000E000 CID B105E00D PID 001BB000 SCS
T39A4 000:220.152   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T39A4 000:224.413   [0][1]: ******** CID B105E00D PID 001BB002 DWT
T39A4 000:224.438   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T39A4 000:228.826   [0][2]: ******** CID B105E00D PID 000BB003 FPB
T39A4 000:228.856   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T39A4 000:231.874   [0][3]: ******** CID B105E00D PID 001BB001 ITM
T39A4 000:231.895   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T39A4 000:235.033   [0][4]: ******** CID B105900D PID 001BB923 TPIU-Lite
T39A4 000:235.627 - 111.330ms returns 0x1BA01477
T39A4 000:235.647 JLINK_GetDLLVersion()
T39A4 000:235.654 - 0.009ms returns 78604
T39A4 000:235.662 JLINK_CORE_GetFound()
T39A4 000:235.668 - 0.009ms returns 0x30000FF
T39A4 000:235.680 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T39A4 000:235.688   Value=0xE00FF000
T39A4 000:235.697 - 0.019ms returns 0
T39A4 000:239.005 JLINK_ReadMem(0xE00FF000, 0x20 Bytes, ...)
T39A4 000:239.039   CPU_ReadMem(32 bytes @ 0xE00FF000)
T39A4 000:239.817   Data:  03 F0 F0 FF 03 20 F0 FF 03 30 F0 FF 03 10 F0 FF ...
T39A4 000:239.831 - 0.828ms returns 0
T39A4 000:239.841 JLINK_ReadMem(0xE000EFF0, 0x10 Bytes, ...)
T39A4 000:239.852   CPU_ReadMem(16 bytes @ 0xE000EFF0)
T39A4 000:240.458   Data:  0D 00 00 00 E0 00 00 00 05 00 00 00 B1 00 00 00
T39A4 000:240.472 - 0.634ms returns 0
T39A4 000:240.482 JLINK_ReadMem(0xE000EFD0, 0x20 Bytes, ...)
T39A4 000:240.493   CPU_ReadMem(32 bytes @ 0xE000EFD0)
T39A4 000:241.290   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T39A4 000:241.308 - 0.829ms returns 0
T39A4 000:241.318 JLINK_ReadMem(0xE0001FF0, 0x10 Bytes, ...)
T39A4 000:241.330   CPU_ReadMem(16 bytes @ 0xE0001FF0)
T39A4 000:242.155   Data:  0D 00 00 00 E0 00 00 00 05 00 00 00 B1 00 00 00
T39A4 000:242.169 - 0.854ms returns 0
T39A4 000:242.179 JLINK_ReadMem(0xE0001FD0, 0x20 Bytes, ...)
T39A4 000:242.190   CPU_ReadMem(32 bytes @ 0xE0001FD0)
T39A4 000:242.942   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T39A4 000:242.956 - 0.779ms returns 0
T39A4 000:242.966 JLINK_ReadMem(0xE0002FF0, 0x10 Bytes, ...)
T39A4 000:242.977   CPU_ReadMem(16 bytes @ 0xE0002FF0)
T39A4 000:243.708   Data:  0D 00 00 00 E0 00 00 00 05 00 00 00 B1 00 00 00
T39A4 000:243.723 - 0.760ms returns 0
T39A4 000:243.733 JLINK_ReadMem(0xE0002FD0, 0x20 Bytes, ...)
T39A4 000:243.745   CPU_ReadMem(32 bytes @ 0xE0002FD0)
T39A4 000:244.587   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T39A4 000:244.602 - 0.871ms returns 0
T39A4 000:244.612 JLINK_ReadMem(0xE0000FF0, 0x10 Bytes, ...)
T39A4 000:244.623   CPU_ReadMem(16 bytes @ 0xE0000FF0)
T39A4 000:245.483   Data:  0D 00 00 00 E0 00 00 00 05 00 00 00 B1 00 00 00
T39A4 000:245.497 - 0.888ms returns 0
T39A4 000:245.507 JLINK_ReadMem(0xE0000FD0, 0x20 Bytes, ...)
T39A4 000:245.518   CPU_ReadMem(32 bytes @ 0xE0000FD0)
T39A4 000:246.516   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T39A4 000:246.530 - 1.026ms returns 0
T39A4 000:246.540 JLINK_ReadMem(0xE0040FF0, 0x10 Bytes, ...)
T39A4 000:246.551   CPU_ReadMem(16 bytes @ 0xE0040FF0)
T39A4 000:247.484   Data:  0D 00 00 00 90 00 00 00 05 00 00 00 B1 00 00 00
T39A4 000:247.513 - 0.975ms returns 0
T39A4 000:247.525 JLINK_ReadMem(0xE0040FD0, 0x20 Bytes, ...)
T39A4 000:247.542   CPU_ReadMem(32 bytes @ 0xE0040FD0)
T39A4 000:248.664   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T39A4 000:248.690 - 1.168ms returns 0
T39A4 000:248.703 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T39A4 000:248.719   CPU_ReadMem(4 bytes @ 0xE000ED00)
T39A4 000:249.625   Data:  31 C2 1F 41
T39A4 000:249.652   Debug reg: CPUID
T39A4 000:249.662 - 0.961ms returns 1 (0x1)
T39A4 000:249.675 JLINK_HasError()
T39A4 000:249.694 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T39A4 000:249.701 - 0.010ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T39A4 000:249.709 JLINK_Reset()
T39A4 000:249.731   CPU is running
T39A4 000:249.743   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T39A4 000:250.691   CPU is running
T39A4 000:250.720   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T39A4 000:255.525   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T39A4 000:257.872   Reset: Reset device via AIRCR.SYSRESETREQ.
T39A4 000:257.890   CPU is running
T39A4 000:257.902   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T39A4 000:311.584   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T39A4 000:312.162   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T39A4 000:316.433   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T39A4 000:322.085   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T39A4 000:325.991   CPU_WriteMem(4 bytes @ 0x********)
T39A4 000:326.525   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T39A4 000:327.099   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:327.629 - 77.929ms
T39A4 000:327.656 JLINK_Halt()
T39A4 000:327.664 - 0.011ms returns 0x00
T39A4 000:327.764 JLINK_IsHalted()
T39A4 000:327.779 - 0.019ms returns TRUE
T39A4 000:327.790 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T39A4 000:327.805   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T39A4 000:328.283   Data:  03 00 03 00
T39A4 000:328.301   Debug reg: DHCSR
T39A4 000:328.311 - 0.525ms returns 1 (0x1)
T39A4 000:328.329 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T39A4 000:328.339   Debug reg: DHCSR
T39A4 000:328.672   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T39A4 000:329.148 - 0.829ms returns 0 (0x00000000)
T39A4 000:329.167 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T39A4 000:329.175   Debug reg: DEMCR
T39A4 000:329.192   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T39A4 000:329.736 - 0.579ms returns 0 (0x00000000)
T39A4 000:338.296 JLINK_GetHWStatus(...)
T39A4 000:338.560 - 0.272ms returns 0
T39A4 000:344.098 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T39A4 000:344.114 - 0.020ms returns 0x06
T39A4 000:344.124 JLINK_GetNumBPUnits(Type = 0xF0)
T39A4 000:344.131 - 0.010ms returns 0x2000
T39A4 000:344.139 JLINK_GetNumWPUnits()
T39A4 000:344.145 - 0.009ms returns 4
T39A4 000:352.094 JLINK_GetSpeed()
T39A4 000:352.113 - 0.022ms returns 4000
T39A4 000:355.557 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T39A4 000:355.579   CPU_ReadMem(4 bytes @ 0xE000E004)
T39A4 000:356.066   Data:  01 00 00 00
T39A4 000:356.081 - 0.527ms returns 1 (0x1)
T39A4 000:356.091 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T39A4 000:356.103   CPU_ReadMem(4 bytes @ 0xE000E004)
T39A4 000:356.783   Data:  01 00 00 00
T39A4 000:356.808 - 0.720ms returns 1 (0x1)
T39A4 000:356.821 JLINK_WriteMem(0x********, 0x1C Bytes, ...)
T39A4 000:356.829   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T39A4 000:356.849   CPU_WriteMem(28 bytes @ 0x********)
T39A4 000:357.947 - 1.150ms returns 0x1C
T39A4 000:357.982 JLINK_ReadMem(0x********, 0x1C Bytes, ...)
T39A4 000:357.999   CPU_ReadMem(28 bytes @ 0x********)
T39A4 000:359.128   Data:  01 00 00 40 00 00 00 00 00 00 00 00 00 00 00 00 ...
T39A4 000:359.160 - 1.181ms returns 0
T39A4 000:359.188 JLINK_Halt()
T39A4 000:359.197 - 0.011ms returns 0x00
T39A4 000:359.205 JLINK_IsHalted()
T39A4 000:359.214 - 0.011ms returns TRUE
T39A4 000:364.264 JLINK_WriteMem(0x20000000, 0x164 Bytes, ...)
T39A4 000:364.279   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T39A4 000:364.562   CPU_WriteMem(356 bytes @ 0x20000000)
T39A4 000:369.321 - 5.080ms returns 0x164
T39A4 000:369.391 JLINK_HasError()
T39A4 000:369.403 JLINK_WriteReg(R0, 0x08000000)
T39A4 000:369.414 - 0.014ms returns 0
T39A4 000:369.423 JLINK_WriteReg(R1, 0x007A1200)
T39A4 000:369.431 - 0.010ms returns 0
T39A4 000:369.439 JLINK_WriteReg(R2, 0x00000001)
T39A4 000:369.446 - 0.010ms returns 0
T39A4 000:369.455 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:369.462 - 0.010ms returns 0
T39A4 000:369.470 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:369.477 - 0.010ms returns 0
T39A4 000:369.485 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:369.492 - 0.010ms returns 0
T39A4 000:369.507 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:369.517 - 0.013ms returns 0
T39A4 000:369.525 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:369.532 - 0.010ms returns 0
T39A4 000:369.540 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:369.554 - 0.017ms returns 0
T39A4 000:369.563 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:369.570 - 0.010ms returns 0
T39A4 000:369.578 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:369.585 - 0.010ms returns 0
T39A4 000:369.593 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:369.600 - 0.010ms returns 0
T39A4 000:369.608 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:369.614 - 0.009ms returns 0
T39A4 000:369.623 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:369.630 - 0.011ms returns 0
T39A4 000:369.639 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:369.646 - 0.010ms returns 0
T39A4 000:369.654 JLINK_WriteReg(R15 (PC), 0x20000038)
T39A4 000:369.663 - 0.012ms returns 0
T39A4 000:369.671 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:369.678 - 0.010ms returns 0
T39A4 000:369.686 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:369.693 - 0.010ms returns 0
T39A4 000:369.709 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:369.716 - 0.010ms returns 0
T39A4 000:369.724 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:369.731 - 0.010ms returns 0
T39A4 000:369.739 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:369.752   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:370.624 - 0.905ms returns 0x00000001
T39A4 000:370.655 JLINK_Go()
T39A4 000:370.668   CPU_WriteMem(2 bytes @ 0x20000000)
T39A4 000:371.222   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:372.085   CPU_WriteMem(4 bytes @ 0xE0002008)
T39A4 000:372.116   CPU_WriteMem(4 bytes @ 0xE000200C)
T39A4 000:372.127   CPU_WriteMem(4 bytes @ 0xE0002010)
T39A4 000:372.137   CPU_WriteMem(4 bytes @ 0xE0002014)
T39A4 000:372.146   CPU_WriteMem(4 bytes @ 0xE0002018)
T39A4 000:372.156   CPU_WriteMem(4 bytes @ 0xE000201C)
T39A4 000:374.741   CPU_WriteMem(4 bytes @ 0xE0001004)
T39A4 000:378.885 - 8.254ms
T39A4 000:378.925 JLINK_IsHalted()
T39A4 000:382.132   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:382.531 - 3.613ms returns TRUE
T39A4 000:382.549 JLINK_ReadReg(R15 (PC))
T39A4 000:382.560 - 0.014ms returns 0x20000000
T39A4 000:382.571 JLINK_ClrBPEx(BPHandle = 0x00000001)
T39A4 000:382.579 - 0.011ms returns 0x00
T39A4 000:382.589 JLINK_ReadReg(R0)
T39A4 000:382.597 - 0.011ms returns 0x00000000
T39A4 000:383.447 JLINK_HasError()
T39A4 000:383.465 JLINK_WriteReg(R0, 0x08000000)
T39A4 000:383.475 - 0.013ms returns 0
T39A4 000:383.484 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:383.491 - 0.011ms returns 0
T39A4 000:383.500 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:383.507 - 0.010ms returns 0
T39A4 000:383.515 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:383.522 - 0.010ms returns 0
T39A4 000:383.531 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:383.538 - 0.010ms returns 0
T39A4 000:383.546 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:383.553 - 0.010ms returns 0
T39A4 000:383.562 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:383.569 - 0.010ms returns 0
T39A4 000:383.577 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:383.584 - 0.010ms returns 0
T39A4 000:383.592 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:383.599 - 0.010ms returns 0
T39A4 000:383.608 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:383.615 - 0.010ms returns 0
T39A4 000:383.623 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:383.630 - 0.010ms returns 0
T39A4 000:383.638 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:383.645 - 0.010ms returns 0
T39A4 000:383.654 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:383.661 - 0.010ms returns 0
T39A4 000:383.669 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:383.677 - 0.010ms returns 0
T39A4 000:383.685 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:383.692 - 0.010ms returns 0
T39A4 000:383.700 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:383.708 - 0.010ms returns 0
T39A4 000:383.716 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:383.723 - 0.010ms returns 0
T39A4 000:383.731 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:383.738 - 0.010ms returns 0
T39A4 000:383.747 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:383.795 - 0.051ms returns 0
T39A4 000:383.804 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:383.811 - 0.010ms returns 0
T39A4 000:383.820 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:383.828 - 0.011ms returns 0x00000002
T39A4 000:383.837 JLINK_Go()
T39A4 000:383.856   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:387.419 - 3.617ms
T39A4 000:387.486 JLINK_IsHalted()
T39A4 000:390.873   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:391.336 - 3.858ms returns TRUE
T39A4 000:391.352 JLINK_ReadReg(R15 (PC))
T39A4 000:391.361 - 0.012ms returns 0x20000000
T39A4 000:391.370 JLINK_ClrBPEx(BPHandle = 0x00000002)
T39A4 000:391.377 - 0.010ms returns 0x00
T39A4 000:391.386 JLINK_ReadReg(R0)
T39A4 000:391.393 - 0.010ms returns 0x00000001
T39A4 000:391.402 JLINK_HasError()
T39A4 000:391.410 JLINK_WriteReg(R0, 0x08000000)
T39A4 000:391.417 - 0.010ms returns 0
T39A4 000:391.426 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:391.432 - 0.010ms returns 0
T39A4 000:391.441 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:391.447 - 0.010ms returns 0
T39A4 000:391.456 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:391.463 - 0.010ms returns 0
T39A4 000:391.471 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:391.478 - 0.010ms returns 0
T39A4 000:391.486 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:391.493 - 0.010ms returns 0
T39A4 000:391.501 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:391.508 - 0.010ms returns 0
T39A4 000:391.516 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:391.523 - 0.010ms returns 0
T39A4 000:391.531 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:391.538 - 0.010ms returns 0
T39A4 000:391.546 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:391.553 - 0.010ms returns 0
T39A4 000:391.561 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:391.568 - 0.010ms returns 0
T39A4 000:391.576 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:391.583 - 0.010ms returns 0
T39A4 000:391.591 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:391.598 - 0.010ms returns 0
T39A4 000:391.606 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:391.613 - 0.010ms returns 0
T39A4 000:391.621 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:391.628 - 0.010ms returns 0
T39A4 000:391.636 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:391.643 - 0.010ms returns 0
T39A4 000:391.651 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:391.658 - 0.010ms returns 0
T39A4 000:391.666 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:391.673 - 0.010ms returns 0
T39A4 000:391.681 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:391.688 - 0.010ms returns 0
T39A4 000:391.696 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:391.703 - 0.010ms returns 0
T39A4 000:391.711 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:391.719 - 0.010ms returns 0x00000003
T39A4 000:391.727 JLINK_Go()
T39A4 000:391.744   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:395.762 - 4.055ms
T39A4 000:395.791 JLINK_IsHalted()
T39A4 000:396.394 - 0.611ms returns FALSE
T39A4 000:396.411 JLINK_HasError()
T39A4 000:413.822 JLINK_IsHalted()
T39A4 000:414.411 - 0.600ms returns FALSE
T39A4 000:414.431 JLINK_HasError()
T39A4 000:415.779 JLINK_IsHalted()
T39A4 000:416.158 - 0.385ms returns FALSE
T39A4 000:416.172 JLINK_HasError()
T39A4 000:418.013 JLINK_IsHalted()
T39A4 000:421.228   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:421.755 - 3.750ms returns TRUE
T39A4 000:421.771 JLINK_ReadReg(R15 (PC))
T39A4 000:421.782 - 0.013ms returns 0x20000000
T39A4 000:421.791 JLINK_ClrBPEx(BPHandle = 0x00000003)
T39A4 000:421.798 - 0.010ms returns 0x00
T39A4 000:421.807 JLINK_ReadReg(R0)
T39A4 000:421.814 - 0.010ms returns 0x00000000
T39A4 000:422.359 JLINK_HasError()
T39A4 000:422.372 JLINK_WriteReg(R0, 0x08000400)
T39A4 000:422.381 - 0.012ms returns 0
T39A4 000:422.389 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:422.396 - 0.010ms returns 0
T39A4 000:422.404 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:422.411 - 0.009ms returns 0
T39A4 000:422.419 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:422.425 - 0.009ms returns 0
T39A4 000:422.433 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:422.440 - 0.009ms returns 0
T39A4 000:422.451 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:422.460 - 0.012ms returns 0
T39A4 000:422.468 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:422.475 - 0.009ms returns 0
T39A4 000:422.483 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:422.489 - 0.009ms returns 0
T39A4 000:422.497 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:422.504 - 0.009ms returns 0
T39A4 000:422.512 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:422.518 - 0.009ms returns 0
T39A4 000:422.526 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:422.533 - 0.009ms returns 0
T39A4 000:422.541 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:422.547 - 0.009ms returns 0
T39A4 000:422.555 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:422.562 - 0.009ms returns 0
T39A4 000:422.570 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:422.577 - 0.010ms returns 0
T39A4 000:422.585 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:422.591 - 0.010ms returns 0
T39A4 000:422.599 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:422.606 - 0.009ms returns 0
T39A4 000:422.614 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:422.621 - 0.009ms returns 0
T39A4 000:422.629 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:422.635 - 0.009ms returns 0
T39A4 000:422.643 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:422.650 - 0.009ms returns 0
T39A4 000:422.658 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:422.664 - 0.009ms returns 0
T39A4 000:422.672 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:422.680 - 0.011ms returns 0x00000004
T39A4 000:422.689 JLINK_Go()
T39A4 000:422.703   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:426.803 - 4.218ms
T39A4 000:426.934 JLINK_IsHalted()
T39A4 000:430.477   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:431.350 - 4.439ms returns TRUE
T39A4 000:431.384 JLINK_ReadReg(R15 (PC))
T39A4 000:431.398 - 0.017ms returns 0x20000000
T39A4 000:431.407 JLINK_ClrBPEx(BPHandle = 0x00000004)
T39A4 000:431.416 - 0.012ms returns 0x00
T39A4 000:431.425 JLINK_ReadReg(R0)
T39A4 000:431.433 - 0.011ms returns 0x00000001
T39A4 000:431.443 JLINK_HasError()
T39A4 000:431.453 JLINK_WriteReg(R0, 0x08000400)
T39A4 000:431.461 - 0.011ms returns 0
T39A4 000:431.470 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:431.478 - 0.011ms returns 0
T39A4 000:431.487 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:431.495 - 0.011ms returns 0
T39A4 000:431.503 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:431.511 - 0.011ms returns 0
T39A4 000:431.520 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:431.527 - 0.011ms returns 0
T39A4 000:431.536 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:431.543 - 0.011ms returns 0
T39A4 000:431.552 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:431.560 - 0.011ms returns 0
T39A4 000:431.569 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:431.576 - 0.011ms returns 0
T39A4 000:431.585 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:431.593 - 0.011ms returns 0
T39A4 000:431.602 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:431.610 - 0.011ms returns 0
T39A4 000:431.619 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:431.627 - 0.011ms returns 0
T39A4 000:431.636 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:431.644 - 0.011ms returns 0
T39A4 000:431.653 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:431.661 - 0.011ms returns 0
T39A4 000:431.669 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:431.678 - 0.011ms returns 0
T39A4 000:431.686 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:431.694 - 0.011ms returns 0
T39A4 000:431.703 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:431.710 - 0.011ms returns 0
T39A4 000:431.719 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:431.730 - 0.015ms returns 0
T39A4 000:431.740 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:431.748 - 0.011ms returns 0
T39A4 000:431.757 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:431.764 - 0.010ms returns 0
T39A4 000:431.773 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:431.780 - 0.010ms returns 0
T39A4 000:431.789 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:431.798 - 0.012ms returns 0x00000005
T39A4 000:431.807 JLINK_Go()
T39A4 000:431.827   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:436.079 - 4.292ms
T39A4 000:436.115 JLINK_IsHalted()
T39A4 000:436.888 - 0.799ms returns FALSE
T39A4 000:436.931 JLINK_HasError()
T39A4 000:439.062 JLINK_IsHalted()
T39A4 000:439.859 - 0.821ms returns FALSE
T39A4 000:439.896 JLINK_HasError()
T39A4 000:441.095 JLINK_IsHalted()
T39A4 000:441.671 - 0.591ms returns FALSE
T39A4 000:441.700 JLINK_HasError()
T39A4 000:443.859 JLINK_IsHalted()
T39A4 000:444.527 - 0.680ms returns FALSE
T39A4 000:444.552 JLINK_HasError()
T39A4 000:445.908 JLINK_IsHalted()
T39A4 000:446.555 - 0.670ms returns FALSE
T39A4 000:446.592 JLINK_HasError()
T39A4 000:448.021 JLINK_IsHalted()
T39A4 000:448.772 - 0.778ms returns FALSE
T39A4 000:448.815 JLINK_HasError()
T39A4 000:450.053 JLINK_IsHalted()
T39A4 000:450.806 - 0.778ms returns FALSE
T39A4 000:450.846 JLINK_HasError()
T39A4 000:453.063 JLINK_IsHalted()
T39A4 000:453.756 - 0.720ms returns FALSE
T39A4 000:453.801 JLINK_HasError()
T39A4 000:454.993 JLINK_IsHalted()
T39A4 000:455.729 - 0.763ms returns FALSE
T39A4 000:455.773 JLINK_HasError()
T39A4 000:457.014 JLINK_IsHalted()
T39A4 000:460.726   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:461.286 - 4.285ms returns TRUE
T39A4 000:461.313 JLINK_ReadReg(R15 (PC))
T39A4 000:461.329 - 0.021ms returns 0x20000000
T39A4 000:461.344 JLINK_ClrBPEx(BPHandle = 0x00000005)
T39A4 000:461.356 - 0.018ms returns 0x00
T39A4 000:461.371 JLINK_ReadReg(R0)
T39A4 000:461.382 - 0.017ms returns 0x00000000
T39A4 000:462.244 JLINK_HasError()
T39A4 000:462.267 JLINK_WriteReg(R0, 0x08000800)
T39A4 000:462.282 - 0.020ms returns 0
T39A4 000:462.296 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:462.308 - 0.016ms returns 0
T39A4 000:462.321 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:462.332 - 0.016ms returns 0
T39A4 000:462.346 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:462.357 - 0.016ms returns 0
T39A4 000:462.370 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:462.381 - 0.016ms returns 0
T39A4 000:462.394 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:462.405 - 0.016ms returns 0
T39A4 000:462.419 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:462.430 - 0.016ms returns 0
T39A4 000:462.443 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:462.454 - 0.016ms returns 0
T39A4 000:462.467 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:462.478 - 0.016ms returns 0
T39A4 000:462.492 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:462.503 - 0.016ms returns 0
T39A4 000:462.516 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:462.527 - 0.016ms returns 0
T39A4 000:462.540 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:462.551 - 0.016ms returns 0
T39A4 000:462.565 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:462.576 - 0.016ms returns 0
T39A4 000:462.589 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:462.601 - 0.017ms returns 0
T39A4 000:462.614 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:462.625 - 0.016ms returns 0
T39A4 000:462.640 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:462.667 - 0.037ms returns 0
T39A4 000:462.688 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:462.701 - 0.018ms returns 0
T39A4 000:462.715 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:462.726 - 0.016ms returns 0
T39A4 000:462.744 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:462.756 - 0.016ms returns 0
T39A4 000:462.772 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:462.783 - 0.016ms returns 0
T39A4 000:462.798 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:462.812 - 0.019ms returns 0x00000006
T39A4 000:462.826 JLINK_Go()
T39A4 000:462.848   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:466.642 - 3.831ms
T39A4 000:466.678 JLINK_IsHalted()
T39A4 000:470.023   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:470.560 - 3.895ms returns TRUE
T39A4 000:470.590 JLINK_ReadReg(R15 (PC))
T39A4 000:470.606 - 0.021ms returns 0x20000000
T39A4 000:470.621 JLINK_ClrBPEx(BPHandle = 0x00000006)
T39A4 000:470.634 - 0.018ms returns 0x00
T39A4 000:470.650 JLINK_ReadReg(R0)
T39A4 000:470.665 - 0.020ms returns 0x00000001
T39A4 000:470.683 JLINK_HasError()
T39A4 000:470.786 JLINK_WriteReg(R0, 0x08000800)
T39A4 000:470.812 - 0.032ms returns 0
T39A4 000:470.827 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:470.840 - 0.022ms returns 0
T39A4 000:470.862 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:470.874 - 0.017ms returns 0
T39A4 000:470.888 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:470.900 - 0.017ms returns 0
T39A4 000:470.914 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:470.925 - 0.017ms returns 0
T39A4 000:470.939 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:470.950 - 0.017ms returns 0
T39A4 000:470.964 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:470.976 - 0.017ms returns 0
T39A4 000:470.989 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:471.001 - 0.017ms returns 0
T39A4 000:471.015 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:471.026 - 0.017ms returns 0
T39A4 000:471.040 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:471.051 - 0.017ms returns 0
T39A4 000:471.065 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:471.077 - 0.017ms returns 0
T39A4 000:471.090 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:471.101 - 0.016ms returns 0
T39A4 000:471.115 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:471.127 - 0.017ms returns 0
T39A4 000:471.140 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:471.152 - 0.017ms returns 0
T39A4 000:471.166 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:471.177 - 0.017ms returns 0
T39A4 000:471.191 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:471.203 - 0.017ms returns 0
T39A4 000:471.216 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:471.228 - 0.016ms returns 0
T39A4 000:471.241 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:471.253 - 0.017ms returns 0
T39A4 000:471.266 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:471.278 - 0.016ms returns 0
T39A4 000:471.291 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:471.303 - 0.017ms returns 0
T39A4 000:471.317 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:471.330 - 0.019ms returns 0x00000007
T39A4 000:471.344 JLINK_Go()
T39A4 000:471.367   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:475.034 - 3.700ms
T39A4 000:475.053 JLINK_IsHalted()
T39A4 000:475.566 - 0.521ms returns FALSE
T39A4 000:475.583 JLINK_HasError()
T39A4 000:478.862 JLINK_IsHalted()
T39A4 000:479.396 - 0.543ms returns FALSE
T39A4 000:479.413 JLINK_HasError()
T39A4 000:481.020 JLINK_IsHalted()
T39A4 000:481.592 - 0.585ms returns FALSE
T39A4 000:481.615 JLINK_HasError()
T39A4 000:483.008 JLINK_IsHalted()
T39A4 000:483.848 - 0.861ms returns FALSE
T39A4 000:483.881 JLINK_HasError()
T39A4 000:485.916 JLINK_IsHalted()
T39A4 000:486.681 - 0.800ms returns FALSE
T39A4 000:486.733 JLINK_HasError()
T39A4 000:487.977 JLINK_IsHalted()
T39A4 000:488.567 - 0.612ms returns FALSE
T39A4 000:488.602 JLINK_HasError()
T39A4 000:493.761 JLINK_IsHalted()
T39A4 000:494.419 - 0.670ms returns FALSE
T39A4 000:494.442 JLINK_HasError()
T39A4 000:495.785 JLINK_IsHalted()
T39A4 000:496.528 - 0.768ms returns FALSE
T39A4 000:496.569 JLINK_HasError()
T39A4 000:498.015 JLINK_IsHalted()
T39A4 000:501.811   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:502.451 - 4.458ms returns TRUE
T39A4 000:502.487 JLINK_ReadReg(R15 (PC))
T39A4 000:502.502 - 0.019ms returns 0x20000000
T39A4 000:502.513 JLINK_ClrBPEx(BPHandle = 0x00000007)
T39A4 000:502.523 - 0.014ms returns 0x00
T39A4 000:502.538 JLINK_ReadReg(R0)
T39A4 000:502.548 - 0.013ms returns 0x00000000
T39A4 000:504.999 JLINK_HasError()
T39A4 000:505.023 JLINK_WriteReg(R0, 0x08000C00)
T39A4 000:505.036 - 0.017ms returns 0
T39A4 000:505.047 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:505.056 - 0.013ms returns 0
T39A4 000:505.067 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:505.076 - 0.013ms returns 0
T39A4 000:505.086 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:505.095 - 0.012ms returns 0
T39A4 000:505.105 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:505.114 - 0.012ms returns 0
T39A4 000:505.124 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:505.133 - 0.012ms returns 0
T39A4 000:505.143 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:505.152 - 0.012ms returns 0
T39A4 000:505.163 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:505.185 - 0.026ms returns 0
T39A4 000:505.195 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:505.202 - 0.011ms returns 0
T39A4 000:505.212 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:505.220 - 0.060ms returns 0
T39A4 000:505.283 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:505.292 - 0.012ms returns 0
T39A4 000:505.301 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:505.309 - 0.011ms returns 0
T39A4 000:505.318 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:505.326 - 0.011ms returns 0
T39A4 000:505.336 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:505.344 - 0.012ms returns 0
T39A4 000:505.354 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:505.362 - 0.011ms returns 0
T39A4 000:505.371 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:505.379 - 0.011ms returns 0
T39A4 000:505.388 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:505.396 - 0.011ms returns 0
T39A4 000:505.406 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:505.414 - 0.011ms returns 0
T39A4 000:505.423 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:505.431 - 0.011ms returns 0
T39A4 000:505.440 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:505.448 - 0.011ms returns 0
T39A4 000:505.458 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:505.467 - 0.013ms returns 0x00000008
T39A4 000:505.477 JLINK_Go()
T39A4 000:505.495   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:509.309 - 3.842ms
T39A4 000:509.328 JLINK_IsHalted()
T39A4 000:512.712   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:513.243 - 3.924ms returns TRUE
T39A4 000:513.261 JLINK_ReadReg(R15 (PC))
T39A4 000:513.272 - 0.014ms returns 0x20000000
T39A4 000:513.282 JLINK_ClrBPEx(BPHandle = 0x00000008)
T39A4 000:513.290 - 0.012ms returns 0x00
T39A4 000:513.299 JLINK_ReadReg(R0)
T39A4 000:513.307 - 0.011ms returns 0x00000001
T39A4 000:513.317 JLINK_HasError()
T39A4 000:513.326 JLINK_WriteReg(R0, 0x08000C00)
T39A4 000:513.335 - 0.012ms returns 0
T39A4 000:513.344 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:513.352 - 0.011ms returns 0
T39A4 000:513.361 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:513.369 - 0.011ms returns 0
T39A4 000:513.378 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:513.386 - 0.011ms returns 0
T39A4 000:513.395 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:513.403 - 0.011ms returns 0
T39A4 000:513.412 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:513.419 - 0.011ms returns 0
T39A4 000:513.429 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:513.436 - 0.011ms returns 0
T39A4 000:513.445 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:513.453 - 0.011ms returns 0
T39A4 000:513.462 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:513.470 - 0.011ms returns 0
T39A4 000:513.479 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:513.487 - 0.011ms returns 0
T39A4 000:513.496 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:513.506 - 0.014ms returns 0
T39A4 000:513.515 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:513.523 - 0.011ms returns 0
T39A4 000:513.532 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:513.539 - 0.011ms returns 0
T39A4 000:513.548 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:513.556 - 0.011ms returns 0
T39A4 000:513.564 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:513.572 - 0.011ms returns 0
T39A4 000:513.581 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:513.588 - 0.011ms returns 0
T39A4 000:513.597 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:513.605 - 0.011ms returns 0
T39A4 000:513.614 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:513.621 - 0.011ms returns 0
T39A4 000:513.630 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:513.637 - 0.010ms returns 0
T39A4 000:513.646 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:513.654 - 0.011ms returns 0
T39A4 000:513.663 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:513.671 - 0.011ms returns 0x00000009
T39A4 000:513.680 JLINK_Go()
T39A4 000:513.694   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:517.260 - 3.591ms
T39A4 000:517.280 JLINK_IsHalted()
T39A4 000:517.776 - 0.505ms returns FALSE
T39A4 000:517.796 JLINK_HasError()
T39A4 000:521.587 JLINK_IsHalted()
T39A4 000:522.379 - 0.814ms returns FALSE
T39A4 000:522.416 JLINK_HasError()
T39A4 000:523.926 JLINK_IsHalted()
T39A4 000:524.705 - 0.804ms returns FALSE
T39A4 000:524.744 JLINK_HasError()
T39A4 000:526.613 JLINK_IsHalted()
T39A4 000:527.272 - 0.672ms returns FALSE
T39A4 000:527.297 JLINK_HasError()
T39A4 000:528.887 JLINK_IsHalted()
T39A4 000:529.641 - 0.779ms returns FALSE
T39A4 000:529.681 JLINK_HasError()
T39A4 000:531.549 JLINK_IsHalted()
T39A4 000:532.307 - 0.785ms returns FALSE
T39A4 000:532.351 JLINK_HasError()
T39A4 000:533.857 JLINK_IsHalted()
T39A4 000:534.413 - 0.572ms returns FALSE
T39A4 000:534.444 JLINK_HasError()
T39A4 000:538.601 JLINK_IsHalted()
T39A4 000:539.180 - 0.593ms returns FALSE
T39A4 000:539.208 JLINK_HasError()
T39A4 000:540.501 JLINK_IsHalted()
T39A4 000:544.319   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:545.048 - 4.574ms returns TRUE
T39A4 000:545.092 JLINK_ReadReg(R15 (PC))
T39A4 000:545.110 - 0.024ms returns 0x20000000
T39A4 000:545.125 JLINK_ClrBPEx(BPHandle = 0x00000009)
T39A4 000:545.138 - 0.018ms returns 0x00
T39A4 000:545.153 JLINK_ReadReg(R0)
T39A4 000:545.164 - 0.017ms returns 0x00000000
T39A4 000:546.051 JLINK_HasError()
T39A4 000:546.078 JLINK_WriteReg(R0, 0x08001000)
T39A4 000:546.093 - 0.020ms returns 0
T39A4 000:546.107 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:546.119 - 0.016ms returns 0
T39A4 000:546.132 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:546.143 - 0.016ms returns 0
T39A4 000:546.157 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:546.168 - 0.016ms returns 0
T39A4 000:546.182 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:546.193 - 0.016ms returns 0
T39A4 000:546.206 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:546.217 - 0.016ms returns 0
T39A4 000:546.230 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:546.241 - 0.016ms returns 0
T39A4 000:546.255 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:546.266 - 0.016ms returns 0
T39A4 000:546.279 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:546.290 - 0.016ms returns 0
T39A4 000:546.303 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:546.314 - 0.016ms returns 0
T39A4 000:546.328 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:546.339 - 0.016ms returns 0
T39A4 000:546.352 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:546.363 - 0.016ms returns 0
T39A4 000:546.376 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:546.387 - 0.016ms returns 0
T39A4 000:546.401 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:546.413 - 0.017ms returns 0
T39A4 000:546.426 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:546.438 - 0.016ms returns 0
T39A4 000:546.451 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:546.464 - 0.018ms returns 0
T39A4 000:546.477 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:546.489 - 0.016ms returns 0
T39A4 000:546.502 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:546.513 - 0.016ms returns 0
T39A4 000:546.527 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:546.538 - 0.016ms returns 0
T39A4 000:546.551 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:546.562 - 0.016ms returns 0
T39A4 000:546.575 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:546.588 - 0.018ms returns 0x0000000A
T39A4 000:546.602 JLINK_Go()
T39A4 000:546.627   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:550.558 - 3.982ms
T39A4 000:550.604 JLINK_IsHalted()
T39A4 000:554.041   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:554.582 - 3.994ms returns TRUE
T39A4 000:554.630 JLINK_ReadReg(R15 (PC))
T39A4 000:554.648 - 0.023ms returns 0x20000000
T39A4 000:554.663 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T39A4 000:554.676 - 0.018ms returns 0x00
T39A4 000:554.692 JLINK_ReadReg(R0)
T39A4 000:554.704 - 0.017ms returns 0x00000001
T39A4 000:554.722 JLINK_HasError()
T39A4 000:554.839 JLINK_WriteReg(R0, 0x08001000)
T39A4 000:554.865 - 0.032ms returns 0
T39A4 000:554.881 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:554.893 - 0.018ms returns 0
T39A4 000:554.907 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:554.919 - 0.017ms returns 0
T39A4 000:554.932 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:554.944 - 0.017ms returns 0
T39A4 000:554.958 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:554.969 - 0.017ms returns 0
T39A4 000:554.983 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:554.994 - 0.017ms returns 0
T39A4 000:555.008 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:555.020 - 0.017ms returns 0
T39A4 000:555.033 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:555.045 - 0.016ms returns 0
T39A4 000:555.124 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:555.143 - 0.024ms returns 0
T39A4 000:555.157 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:555.169 - 0.017ms returns 0
T39A4 000:555.183 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:555.194 - 0.017ms returns 0
T39A4 000:555.208 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:555.219 - 0.017ms returns 0
T39A4 000:555.233 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:555.244 - 0.017ms returns 0
T39A4 000:555.258 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:555.270 - 0.017ms returns 0
T39A4 000:555.284 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:555.296 - 0.017ms returns 0
T39A4 000:555.309 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:555.321 - 0.017ms returns 0
T39A4 000:555.335 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:555.346 - 0.017ms returns 0
T39A4 000:555.360 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:555.372 - 0.017ms returns 0
T39A4 000:555.385 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:555.397 - 0.017ms returns 0
T39A4 000:555.411 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:555.422 - 0.020ms returns 0
T39A4 000:555.440 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:555.454 - 0.019ms returns 0x0000000B
T39A4 000:555.468 JLINK_Go()
T39A4 000:555.491   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:559.410 - 3.965ms
T39A4 000:559.448 JLINK_IsHalted()
T39A4 000:559.967 - 0.532ms returns FALSE
T39A4 000:559.995 JLINK_HasError()
T39A4 000:561.468 JLINK_IsHalted()
T39A4 000:562.298 - 0.856ms returns FALSE
T39A4 000:562.341 JLINK_HasError()
T39A4 000:563.730 JLINK_IsHalted()
T39A4 000:564.449 - 0.746ms returns FALSE
T39A4 000:564.493 JLINK_HasError()
T39A4 000:565.725 JLINK_IsHalted()
T39A4 000:566.357 - 0.654ms returns FALSE
T39A4 000:566.395 JLINK_HasError()
T39A4 000:570.398 JLINK_IsHalted()
T39A4 000:570.904 - 0.517ms returns FALSE
T39A4 000:570.925 JLINK_HasError()
T39A4 000:572.796 JLINK_IsHalted()
T39A4 000:573.363 - 0.590ms returns FALSE
T39A4 000:573.399 JLINK_HasError()
T39A4 000:574.790 JLINK_IsHalted()
T39A4 000:575.565 - 0.823ms returns FALSE
T39A4 000:575.628 JLINK_HasError()
T39A4 000:577.496 JLINK_IsHalted()
T39A4 000:578.156 - 0.672ms returns FALSE
T39A4 000:578.180 JLINK_HasError()
T39A4 000:579.745 JLINK_IsHalted()
T39A4 000:580.300 - 0.573ms returns FALSE
T39A4 000:580.338 JLINK_HasError()
T39A4 000:582.412 JLINK_IsHalted()
T39A4 000:586.064   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:586.626 - 4.230ms returns TRUE
T39A4 000:586.657 JLINK_ReadReg(R15 (PC))
T39A4 000:586.674 - 0.022ms returns 0x20000000
T39A4 000:586.689 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T39A4 000:586.702 - 0.018ms returns 0x00
T39A4 000:586.716 JLINK_ReadReg(R0)
T39A4 000:586.728 - 0.017ms returns 0x00000000
T39A4 000:587.578 JLINK_HasError()
T39A4 000:587.601 JLINK_WriteReg(R0, 0x08001400)
T39A4 000:587.616 - 0.020ms returns 0
T39A4 000:587.630 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:587.641 - 0.016ms returns 0
T39A4 000:587.654 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:587.666 - 0.016ms returns 0
T39A4 000:587.679 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:587.690 - 0.016ms returns 0
T39A4 000:587.703 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:587.715 - 0.016ms returns 0
T39A4 000:587.728 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:587.739 - 0.016ms returns 0
T39A4 000:587.752 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:587.763 - 0.016ms returns 0
T39A4 000:587.777 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:587.788 - 0.016ms returns 0
T39A4 000:587.801 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:587.812 - 0.016ms returns 0
T39A4 000:587.825 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:587.836 - 0.016ms returns 0
T39A4 000:587.850 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:587.861 - 0.016ms returns 0
T39A4 000:587.874 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:587.885 - 0.016ms returns 0
T39A4 000:587.898 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:587.910 - 0.016ms returns 0
T39A4 000:587.923 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:587.935 - 0.017ms returns 0
T39A4 000:587.948 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:588.027 - 0.090ms returns 0
T39A4 000:588.047 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:588.059 - 0.017ms returns 0
T39A4 000:588.072 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:588.083 - 0.016ms returns 0
T39A4 000:588.097 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:588.108 - 0.016ms returns 0
T39A4 000:588.121 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:588.132 - 0.016ms returns 0
T39A4 000:588.146 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:588.157 - 0.016ms returns 0
T39A4 000:588.170 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:588.183 - 0.018ms returns 0x0000000C
T39A4 000:588.197 JLINK_Go()
T39A4 000:588.220   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:592.557 - 4.397ms
T39A4 000:592.614 JLINK_IsHalted()
T39A4 000:596.122   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:596.792 - 4.204ms returns TRUE
T39A4 000:596.836 JLINK_ReadReg(R15 (PC))
T39A4 000:596.854 - 0.023ms returns 0x20000000
T39A4 000:596.868 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T39A4 000:596.881 - 0.018ms returns 0x00
T39A4 000:596.896 JLINK_ReadReg(R0)
T39A4 000:596.907 - 0.017ms returns 0x00000001
T39A4 000:596.922 JLINK_HasError()
T39A4 000:596.937 JLINK_WriteReg(R0, 0x08001400)
T39A4 000:596.950 - 0.018ms returns 0
T39A4 000:596.964 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:596.975 - 0.017ms returns 0
T39A4 000:596.989 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:597.001 - 0.017ms returns 0
T39A4 000:597.014 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:597.026 - 0.017ms returns 0
T39A4 000:597.040 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:597.051 - 0.017ms returns 0
T39A4 000:597.065 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:597.076 - 0.016ms returns 0
T39A4 000:597.090 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:597.101 - 0.017ms returns 0
T39A4 000:597.115 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:597.127 - 0.017ms returns 0
T39A4 000:597.140 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:597.152 - 0.017ms returns 0
T39A4 000:597.166 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:597.177 - 0.017ms returns 0
T39A4 000:597.191 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:597.202 - 0.017ms returns 0
T39A4 000:597.216 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:597.228 - 0.017ms returns 0
T39A4 000:597.241 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:597.253 - 0.016ms returns 0
T39A4 000:597.266 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:597.291 - 0.030ms returns 0
T39A4 000:597.305 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:597.320 - 0.020ms returns 0
T39A4 000:597.335 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:597.353 - 0.027ms returns 0
T39A4 000:597.371 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:597.387 - 0.029ms returns 0
T39A4 000:597.419 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:597.437 - 0.023ms returns 0
T39A4 000:597.451 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:597.463 - 0.017ms returns 0
T39A4 000:597.477 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:597.488 - 0.016ms returns 0
T39A4 000:597.503 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:597.517 - 0.020ms returns 0x0000000D
T39A4 000:597.531 JLINK_Go()
T39A4 000:597.556   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:601.347 - 3.828ms
T39A4 000:601.369 JLINK_IsHalted()
T39A4 000:601.897 - 0.538ms returns FALSE
T39A4 000:601.917 JLINK_HasError()
T39A4 000:605.473 JLINK_IsHalted()
T39A4 000:606.226 - 0.775ms returns FALSE
T39A4 000:606.264 JLINK_HasError()
T39A4 000:607.595 JLINK_IsHalted()
T39A4 000:608.323 - 0.752ms returns FALSE
T39A4 000:608.361 JLINK_HasError()
T39A4 000:610.434 JLINK_IsHalted()
T39A4 000:610.988 - 0.565ms returns FALSE
T39A4 000:611.010 JLINK_HasError()
T39A4 000:612.659 JLINK_IsHalted()
T39A4 000:613.216 - 0.572ms returns FALSE
T39A4 000:613.247 JLINK_HasError()
T39A4 000:614.651 JLINK_IsHalted()
T39A4 000:615.408 - 0.783ms returns FALSE
T39A4 000:615.449 JLINK_HasError()
T39A4 000:616.593 JLINK_IsHalted()
T39A4 000:617.344 - 0.779ms returns FALSE
T39A4 000:617.388 JLINK_HasError()
T39A4 000:618.630 JLINK_IsHalted()
T39A4 000:619.377 - 0.841ms returns FALSE
T39A4 000:619.494 JLINK_HasError()
T39A4 000:621.304 JLINK_IsHalted()
T39A4 000:622.055 - 0.777ms returns FALSE
T39A4 000:622.099 JLINK_HasError()
T39A4 000:623.619 JLINK_IsHalted()
T39A4 000:627.543   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:628.302 - 4.715ms returns TRUE
T39A4 000:628.351 JLINK_ReadReg(R15 (PC))
T39A4 000:628.370 - 0.025ms returns 0x20000000
T39A4 000:628.386 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T39A4 000:628.398 - 0.018ms returns 0x00
T39A4 000:628.413 JLINK_ReadReg(R0)
T39A4 000:628.425 - 0.017ms returns 0x00000000
T39A4 000:630.830 JLINK_HasError()
T39A4 000:630.861 JLINK_WriteReg(R0, 0x08001800)
T39A4 000:630.877 - 0.021ms returns 0
T39A4 000:630.891 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:630.903 - 0.017ms returns 0
T39A4 000:630.917 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:630.928 - 0.016ms returns 0
T39A4 000:630.941 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:630.952 - 0.016ms returns 0
T39A4 000:630.966 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:630.977 - 0.016ms returns 0
T39A4 000:630.996 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:631.008 - 0.016ms returns 0
T39A4 000:631.021 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:631.032 - 0.016ms returns 0
T39A4 000:631.045 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:631.057 - 0.016ms returns 0
T39A4 000:631.070 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:631.081 - 0.016ms returns 0
T39A4 000:631.094 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:631.105 - 0.016ms returns 0
T39A4 000:631.118 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:631.129 - 0.016ms returns 0
T39A4 000:631.143 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:631.154 - 0.016ms returns 0
T39A4 000:631.167 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:631.178 - 0.016ms returns 0
T39A4 000:631.191 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:631.203 - 0.017ms returns 0
T39A4 000:631.219 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:631.230 - 0.016ms returns 0
T39A4 000:631.243 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:631.255 - 0.016ms returns 0
T39A4 000:631.268 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:631.279 - 0.016ms returns 0
T39A4 000:631.292 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:631.303 - 0.016ms returns 0
T39A4 000:631.317 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:631.328 - 0.016ms returns 0
T39A4 000:631.341 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:631.352 - 0.016ms returns 0
T39A4 000:631.366 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:631.379 - 0.018ms returns 0x0000000E
T39A4 000:631.393 JLINK_Go()
T39A4 000:631.418   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:635.289 - 3.910ms
T39A4 000:635.321 JLINK_IsHalted()
T39A4 000:638.708   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:639.350 - 4.050ms returns TRUE
T39A4 000:639.388 JLINK_ReadReg(R15 (PC))
T39A4 000:639.404 - 0.022ms returns 0x20000000
T39A4 000:639.418 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T39A4 000:639.431 - 0.017ms returns 0x00
T39A4 000:639.447 JLINK_ReadReg(R0)
T39A4 000:639.458 - 0.016ms returns 0x00000001
T39A4 000:639.475 JLINK_HasError()
T39A4 000:639.578 JLINK_WriteReg(R0, 0x08001800)
T39A4 000:639.604 - 0.032ms returns 0
T39A4 000:639.620 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:639.632 - 0.017ms returns 0
T39A4 000:639.646 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:639.658 - 0.017ms returns 0
T39A4 000:639.672 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:639.683 - 0.017ms returns 0
T39A4 000:639.697 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:639.708 - 0.017ms returns 0
T39A4 000:639.722 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:639.733 - 0.017ms returns 0
T39A4 000:639.747 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:639.758 - 0.016ms returns 0
T39A4 000:639.772 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:639.783 - 0.017ms returns 0
T39A4 000:639.797 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:639.808 - 0.017ms returns 0
T39A4 000:639.822 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:639.833 - 0.017ms returns 0
T39A4 000:639.847 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:639.858 - 0.017ms returns 0
T39A4 000:639.872 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:639.963 - 0.096ms returns 0
T39A4 000:639.978 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:639.990 - 0.017ms returns 0
T39A4 000:640.003 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:640.015 - 0.017ms returns 0
T39A4 000:640.029 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:640.040 - 0.017ms returns 0
T39A4 000:640.054 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:640.066 - 0.017ms returns 0
T39A4 000:640.079 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:640.091 - 0.017ms returns 0
T39A4 000:640.104 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:640.116 - 0.017ms returns 0
T39A4 000:640.129 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:640.141 - 0.016ms returns 0
T39A4 000:640.154 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:640.166 - 0.016ms returns 0
T39A4 000:640.180 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:640.196 - 0.022ms returns 0x0000000F
T39A4 000:640.210 JLINK_Go()
T39A4 000:640.233   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:644.182 - 3.988ms
T39A4 000:644.215 JLINK_IsHalted()
T39A4 000:644.673 - 0.471ms returns FALSE
T39A4 000:644.700 JLINK_HasError()
T39A4 000:647.388 JLINK_IsHalted()
T39A4 000:648.012 - 0.634ms returns FALSE
T39A4 000:648.032 JLINK_HasError()
T39A4 000:649.393 JLINK_IsHalted()
T39A4 000:650.023 - 0.639ms returns FALSE
T39A4 000:650.042 JLINK_HasError()
T39A4 000:651.247 JLINK_IsHalted()
T39A4 000:651.866 - 0.629ms returns FALSE
T39A4 000:651.886 JLINK_HasError()
T39A4 000:653.320 JLINK_IsHalted()
T39A4 000:653.923 - 0.613ms returns FALSE
T39A4 000:653.945 JLINK_HasError()
T39A4 000:655.511 JLINK_IsHalted()
T39A4 000:656.330 - 0.845ms returns FALSE
T39A4 000:656.371 JLINK_HasError()
T39A4 000:658.446 JLINK_IsHalted()
T39A4 000:659.168 - 0.756ms returns FALSE
T39A4 000:659.221 JLINK_HasError()
T39A4 000:660.560 JLINK_IsHalted()
T39A4 000:661.132 - 0.587ms returns FALSE
T39A4 000:661.159 JLINK_HasError()
T39A4 000:665.972 JLINK_IsHalted()
T39A4 000:669.837   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:670.689 - 4.739ms returns TRUE
T39A4 000:670.724 JLINK_ReadReg(R15 (PC))
T39A4 000:670.738 - 0.018ms returns 0x20000000
T39A4 000:670.749 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T39A4 000:670.758 - 0.013ms returns 0x00
T39A4 000:670.769 JLINK_ReadReg(R0)
T39A4 000:670.778 - 0.012ms returns 0x00000000
T39A4 000:671.462 JLINK_HasError()
T39A4 000:671.485 JLINK_WriteReg(R0, 0x08001C00)
T39A4 000:671.497 - 0.015ms returns 0
T39A4 000:671.507 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:671.516 - 0.012ms returns 0
T39A4 000:671.526 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:671.534 - 0.014ms returns 0
T39A4 000:671.546 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:671.554 - 0.012ms returns 0
T39A4 000:671.564 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:671.572 - 0.012ms returns 0
T39A4 000:671.582 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:671.590 - 0.012ms returns 0
T39A4 000:671.600 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:671.608 - 0.012ms returns 0
T39A4 000:671.618 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:671.626 - 0.012ms returns 0
T39A4 000:671.636 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:671.644 - 0.012ms returns 0
T39A4 000:671.654 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:671.662 - 0.012ms returns 0
T39A4 000:671.672 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:671.680 - 0.012ms returns 0
T39A4 000:671.690 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:671.698 - 0.012ms returns 0
T39A4 000:671.708 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:671.716 - 0.012ms returns 0
T39A4 000:671.726 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:671.734 - 0.012ms returns 0
T39A4 000:671.744 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:671.752 - 0.012ms returns 0
T39A4 000:671.762 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:671.770 - 0.012ms returns 0
T39A4 000:671.780 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:671.788 - 0.012ms returns 0
T39A4 000:671.798 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:671.806 - 0.012ms returns 0
T39A4 000:671.816 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:671.824 - 0.012ms returns 0
T39A4 000:671.839 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:671.850 - 0.015ms returns 0
T39A4 000:671.860 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:671.869 - 0.013ms returns 0x00000010
T39A4 000:671.879 JLINK_Go()
T39A4 000:671.898   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:675.768 - 3.923ms
T39A4 000:675.818 JLINK_IsHalted()
T39A4 000:679.418   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:680.142 - 4.350ms returns TRUE
T39A4 000:680.182 JLINK_ReadReg(R15 (PC))
T39A4 000:680.200 - 0.022ms returns 0x20000000
T39A4 000:680.213 JLINK_ClrBPEx(BPHandle = 0x00000010)
T39A4 000:680.225 - 0.016ms returns 0x00
T39A4 000:680.238 JLINK_ReadReg(R0)
T39A4 000:680.249 - 0.016ms returns 0x00000001
T39A4 000:680.263 JLINK_HasError()
T39A4 000:680.276 JLINK_WriteReg(R0, 0x08001C00)
T39A4 000:680.288 - 0.016ms returns 0
T39A4 000:680.300 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:680.311 - 0.015ms returns 0
T39A4 000:680.323 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:680.334 - 0.015ms returns 0
T39A4 000:680.347 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:680.357 - 0.015ms returns 0
T39A4 000:680.370 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:680.380 - 0.015ms returns 0
T39A4 000:680.393 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:680.403 - 0.015ms returns 0
T39A4 000:680.416 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:680.426 - 0.015ms returns 0
T39A4 000:680.439 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:680.449 - 0.015ms returns 0
T39A4 000:680.462 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:680.473 - 0.015ms returns 0
T39A4 000:680.485 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:680.496 - 0.015ms returns 0
T39A4 000:680.508 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:680.522 - 0.020ms returns 0
T39A4 000:680.540 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:680.552 - 0.017ms returns 0
T39A4 000:680.565 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:680.576 - 0.016ms returns 0
T39A4 000:680.589 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:680.601 - 0.017ms returns 0
T39A4 000:680.614 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:680.625 - 0.016ms returns 0
T39A4 000:680.638 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:680.649 - 0.016ms returns 0
T39A4 000:680.662 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:680.672 - 0.015ms returns 0
T39A4 000:680.685 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:680.695 - 0.015ms returns 0
T39A4 000:680.707 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:680.718 - 0.015ms returns 0
T39A4 000:680.730 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:680.740 - 0.027ms returns 0
T39A4 000:680.765 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:680.777 - 0.017ms returns 0x00000011
T39A4 000:680.790 JLINK_Go()
T39A4 000:680.812   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:684.755 - 3.994ms
T39A4 000:684.801 JLINK_IsHalted()
T39A4 000:685.505 - 0.731ms returns FALSE
T39A4 000:685.549 JLINK_HasError()
T39A4 000:686.826 JLINK_IsHalted()
T39A4 000:687.587 - 0.789ms returns FALSE
T39A4 000:687.632 JLINK_HasError()
T39A4 000:688.802 JLINK_IsHalted()
T39A4 000:689.611 - 0.838ms returns FALSE
T39A4 000:689.657 JLINK_HasError()
T39A4 000:691.558 JLINK_IsHalted()
T39A4 000:692.326 - 0.797ms returns FALSE
T39A4 000:692.372 JLINK_HasError()
T39A4 000:694.732 JLINK_IsHalted()
T39A4 000:695.480 - 0.775ms returns FALSE
T39A4 000:695.523 JLINK_HasError()
T39A4 000:698.774 JLINK_IsHalted()
T39A4 000:699.516 - 0.773ms returns FALSE
T39A4 000:699.567 JLINK_HasError()
T39A4 000:701.574 JLINK_IsHalted()
T39A4 000:702.305 - 0.757ms returns FALSE
T39A4 000:702.348 JLINK_HasError()
T39A4 000:703.821 JLINK_IsHalted()
T39A4 000:704.568 - 0.775ms returns FALSE
T39A4 000:704.613 JLINK_HasError()
T39A4 000:706.750 JLINK_IsHalted()
T39A4 000:710.349   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:711.067 - 4.333ms returns TRUE
T39A4 000:711.098 JLINK_ReadReg(R15 (PC))
T39A4 000:711.115 - 0.023ms returns 0x20000000
T39A4 000:711.130 JLINK_ClrBPEx(BPHandle = 0x00000011)
T39A4 000:711.143 - 0.018ms returns 0x00
T39A4 000:711.157 JLINK_ReadReg(R0)
T39A4 000:711.174 - 0.026ms returns 0x00000000
T39A4 000:712.463 JLINK_HasError()
T39A4 000:712.482 JLINK_WriteReg(R0, 0x08002000)
T39A4 000:712.493 - 0.015ms returns 0
T39A4 000:712.504 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:712.513 - 0.012ms returns 0
T39A4 000:712.523 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:712.531 - 0.012ms returns 0
T39A4 000:712.541 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:712.549 - 0.012ms returns 0
T39A4 000:712.558 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:712.566 - 0.012ms returns 0
T39A4 000:712.576 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:712.584 - 0.012ms returns 0
T39A4 000:712.594 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:712.602 - 0.012ms returns 0
T39A4 000:712.612 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:712.620 - 0.011ms returns 0
T39A4 000:712.629 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:712.638 - 0.012ms returns 0
T39A4 000:712.647 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:712.655 - 0.011ms returns 0
T39A4 000:712.665 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:712.673 - 0.012ms returns 0
T39A4 000:712.683 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:712.691 - 0.012ms returns 0
T39A4 000:712.701 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:712.709 - 0.011ms returns 0
T39A4 000:712.718 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:712.727 - 0.012ms returns 0
T39A4 000:712.737 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:712.745 - 0.012ms returns 0
T39A4 000:712.755 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:712.763 - 0.012ms returns 0
T39A4 000:712.773 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:712.781 - 0.012ms returns 0
T39A4 000:712.791 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:712.799 - 0.012ms returns 0
T39A4 000:712.809 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:712.817 - 0.012ms returns 0
T39A4 000:712.827 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:712.835 - 0.012ms returns 0
T39A4 000:712.845 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:712.854 - 0.013ms returns 0x00000012
T39A4 000:712.864 JLINK_Go()
T39A4 000:712.881   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:716.703 - 3.849ms
T39A4 000:716.726 JLINK_IsHalted()
T39A4 000:720.490   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:721.356 - 4.658ms returns TRUE
T39A4 000:721.404 JLINK_ReadReg(R15 (PC))
T39A4 000:721.420 - 0.021ms returns 0x20000000
T39A4 000:721.433 JLINK_ClrBPEx(BPHandle = 0x00000012)
T39A4 000:721.444 - 0.015ms returns 0x00
T39A4 000:721.456 JLINK_ReadReg(R0)
T39A4 000:721.466 - 0.014ms returns 0x00000001
T39A4 000:721.480 JLINK_HasError()
T39A4 000:721.492 JLINK_WriteReg(R0, 0x08002000)
T39A4 000:721.503 - 0.015ms returns 0
T39A4 000:721.514 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:721.524 - 0.013ms returns 0
T39A4 000:721.619 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:721.642 - 0.028ms returns 0
T39A4 000:721.655 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:721.666 - 0.014ms returns 0
T39A4 000:721.677 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:721.687 - 0.014ms returns 0
T39A4 000:721.698 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:721.707 - 0.014ms returns 0
T39A4 000:721.719 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:721.728 - 0.013ms returns 0
T39A4 000:721.739 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:721.749 - 0.013ms returns 0
T39A4 000:721.760 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:721.769 - 0.014ms returns 0
T39A4 000:721.780 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:721.790 - 0.013ms returns 0
T39A4 000:721.801 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:721.810 - 0.013ms returns 0
T39A4 000:721.821 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:721.831 - 0.013ms returns 0
T39A4 000:721.842 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:721.851 - 0.013ms returns 0
T39A4 000:721.862 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:721.872 - 0.014ms returns 0
T39A4 000:721.884 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:721.893 - 0.014ms returns 0
T39A4 000:721.904 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:721.914 - 0.013ms returns 0
T39A4 000:721.925 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:721.934 - 0.013ms returns 0
T39A4 000:721.945 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:721.963 - 0.022ms returns 0
T39A4 000:721.975 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:721.984 - 0.013ms returns 0
T39A4 000:721.995 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:722.004 - 0.013ms returns 0
T39A4 000:722.016 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:722.027 - 0.023ms returns 0x00000013
T39A4 000:722.046 JLINK_Go()
T39A4 000:722.068   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:725.848 - 3.826ms
T39A4 000:725.888 JLINK_IsHalted()
T39A4 000:726.390 - 0.541ms returns FALSE
T39A4 000:726.442 JLINK_HasError()
T39A4 000:731.405 JLINK_IsHalted()
T39A4 000:732.146 - 0.768ms returns FALSE
T39A4 000:732.189 JLINK_HasError()
T39A4 000:733.745 JLINK_IsHalted()
T39A4 000:734.519 - 0.801ms returns FALSE
T39A4 000:734.563 JLINK_HasError()
T39A4 000:736.649 JLINK_IsHalted()
T39A4 000:737.400 - 0.778ms returns FALSE
T39A4 000:737.446 JLINK_HasError()
T39A4 000:738.677 JLINK_IsHalted()
T39A4 000:739.434 - 0.785ms returns FALSE
T39A4 000:739.479 JLINK_HasError()
T39A4 000:740.666 JLINK_IsHalted()
T39A4 000:741.249 - 0.602ms returns FALSE
T39A4 000:741.284 JLINK_HasError()
T39A4 000:744.539 JLINK_IsHalted()
T39A4 000:745.286 - 0.777ms returns FALSE
T39A4 000:745.333 JLINK_HasError()
T39A4 000:746.717 JLINK_IsHalted()
T39A4 000:747.473 - 0.784ms returns FALSE
T39A4 000:747.522 JLINK_HasError()
T39A4 000:749.622 JLINK_IsHalted()
T39A4 000:753.450   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:754.211 - 4.616ms returns TRUE
T39A4 000:754.255 JLINK_ReadReg(R15 (PC))
T39A4 000:754.273 - 0.028ms returns 0x20000000
T39A4 000:754.292 JLINK_ClrBPEx(BPHandle = 0x00000013)
T39A4 000:754.306 - 0.018ms returns 0x00
T39A4 000:754.320 JLINK_ReadReg(R0)
T39A4 000:754.332 - 0.017ms returns 0x00000000
T39A4 000:755.197 JLINK_HasError()
T39A4 000:755.223 JLINK_WriteReg(R0, 0x08002400)
T39A4 000:755.238 - 0.020ms returns 0
T39A4 000:755.252 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:755.263 - 0.016ms returns 0
T39A4 000:755.277 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:755.288 - 0.016ms returns 0
T39A4 000:755.301 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:755.315 - 0.019ms returns 0
T39A4 000:755.329 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:755.348 - 0.031ms returns 0
T39A4 000:755.378 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:755.395 - 0.021ms returns 0
T39A4 000:755.409 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:755.420 - 0.016ms returns 0
T39A4 000:755.433 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:755.444 - 0.016ms returns 0
T39A4 000:755.458 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:755.469 - 0.016ms returns 0
T39A4 000:755.482 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:755.493 - 0.016ms returns 0
T39A4 000:755.506 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:755.517 - 0.016ms returns 0
T39A4 000:755.531 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:755.542 - 0.016ms returns 0
T39A4 000:755.555 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:755.566 - 0.016ms returns 0
T39A4 000:755.579 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:755.591 - 0.017ms returns 0
T39A4 000:755.605 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:755.616 - 0.016ms returns 0
T39A4 000:755.629 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:755.640 - 0.016ms returns 0
T39A4 000:755.654 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:755.665 - 0.016ms returns 0
T39A4 000:755.678 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:755.689 - 0.016ms returns 0
T39A4 000:755.702 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:755.713 - 0.016ms returns 0
T39A4 000:755.727 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:755.738 - 0.016ms returns 0
T39A4 000:755.751 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:755.765 - 0.019ms returns 0x00000014
T39A4 000:755.778 JLINK_Go()
T39A4 000:755.815   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:759.719 - 3.970ms
T39A4 000:759.780 JLINK_IsHalted()
T39A4 000:763.329   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:764.145 - 4.400ms returns TRUE
T39A4 000:764.201 JLINK_ReadReg(R15 (PC))
T39A4 000:764.225 - 0.034ms returns 0x20000000
T39A4 000:764.255 JLINK_ClrBPEx(BPHandle = 0x00000014)
T39A4 000:764.284 - 0.034ms returns 0x00
T39A4 000:764.300 JLINK_ReadReg(R0)
T39A4 000:764.312 - 0.017ms returns 0x00000001
T39A4 000:764.327 JLINK_HasError()
T39A4 000:764.341 JLINK_WriteReg(R0, 0x08002400)
T39A4 000:764.354 - 0.017ms returns 0
T39A4 000:764.368 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:764.379 - 0.016ms returns 0
T39A4 000:764.392 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:764.403 - 0.016ms returns 0
T39A4 000:764.417 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:764.428 - 0.016ms returns 0
T39A4 000:764.441 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:764.452 - 0.016ms returns 0
T39A4 000:764.465 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:764.477 - 0.016ms returns 0
T39A4 000:764.490 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:764.501 - 0.016ms returns 0
T39A4 000:764.514 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:764.525 - 0.016ms returns 0
T39A4 000:764.539 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:764.550 - 0.016ms returns 0
T39A4 000:764.563 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:764.574 - 0.016ms returns 0
T39A4 000:764.587 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:764.599 - 0.016ms returns 0
T39A4 000:764.612 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:764.623 - 0.016ms returns 0
T39A4 000:764.636 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:764.647 - 0.016ms returns 0
T39A4 000:764.660 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:764.672 - 0.016ms returns 0
T39A4 000:764.685 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:764.696 - 0.016ms returns 0
T39A4 000:764.710 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:764.721 - 0.016ms returns 0
T39A4 000:764.734 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:764.746 - 0.016ms returns 0
T39A4 000:764.759 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:764.770 - 0.016ms returns 0
T39A4 000:764.783 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:764.794 - 0.016ms returns 0
T39A4 000:764.808 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:764.819 - 0.016ms returns 0
T39A4 000:764.832 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:764.845 - 0.018ms returns 0x00000015
T39A4 000:764.858 JLINK_Go()
T39A4 000:764.882   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:769.221 - 4.390ms
T39A4 000:769.264 JLINK_IsHalted()
T39A4 000:769.991 - 0.755ms returns FALSE
T39A4 000:770.037 JLINK_HasError()
T39A4 000:771.343 JLINK_IsHalted()
T39A4 000:772.150 - 0.855ms returns FALSE
T39A4 000:772.221 JLINK_HasError()
T39A4 000:774.324 JLINK_IsHalted()
T39A4 000:775.079 - 0.783ms returns FALSE
T39A4 000:775.125 JLINK_HasError()
T39A4 000:777.089 JLINK_IsHalted()
T39A4 000:777.748 - 0.673ms returns FALSE
T39A4 000:777.776 JLINK_HasError()
T39A4 000:779.316 JLINK_IsHalted()
T39A4 000:780.077 - 0.789ms returns FALSE
T39A4 000:780.121 JLINK_HasError()
T39A4 000:782.193 JLINK_IsHalted()
T39A4 000:782.961 - 0.795ms returns FALSE
T39A4 000:783.004 JLINK_HasError()
T39A4 000:784.264 JLINK_IsHalted()
T39A4 000:785.010 - 0.776ms returns FALSE
T39A4 000:785.057 JLINK_HasError()
T39A4 000:786.260 JLINK_IsHalted()
T39A4 000:786.997 - 0.779ms returns FALSE
T39A4 000:787.057 JLINK_HasError()
T39A4 000:791.139 JLINK_IsHalted()
T39A4 000:794.410   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:795.174 - 4.060ms returns TRUE
T39A4 000:795.212 JLINK_ReadReg(R15 (PC))
T39A4 000:795.228 - 0.020ms returns 0x20000000
T39A4 000:795.240 JLINK_ClrBPEx(BPHandle = 0x00000015)
T39A4 000:795.250 - 0.014ms returns 0x00
T39A4 000:795.262 JLINK_ReadReg(R0)
T39A4 000:795.271 - 0.014ms returns 0x00000000
T39A4 000:795.992 JLINK_HasError()
T39A4 000:796.018 JLINK_WriteReg(R0, 0x08002800)
T39A4 000:796.031 - 0.018ms returns 0
T39A4 000:796.043 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:796.052 - 0.013ms returns 0
T39A4 000:796.064 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:796.073 - 0.013ms returns 0
T39A4 000:796.083 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:796.093 - 0.013ms returns 0
T39A4 000:796.103 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:796.112 - 0.013ms returns 0
T39A4 000:796.123 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:796.140 - 0.021ms returns 0
T39A4 000:796.151 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:796.161 - 0.013ms returns 0
T39A4 000:796.171 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:796.180 - 0.013ms returns 0
T39A4 000:796.191 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:796.200 - 0.013ms returns 0
T39A4 000:796.211 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:796.220 - 0.013ms returns 0
T39A4 000:796.231 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:796.240 - 0.013ms returns 0
T39A4 000:796.251 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:796.260 - 0.013ms returns 0
T39A4 000:796.271 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:796.280 - 0.013ms returns 0
T39A4 000:796.291 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:796.300 - 0.014ms returns 0
T39A4 000:796.311 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:796.321 - 0.013ms returns 0
T39A4 000:796.331 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:796.341 - 0.013ms returns 0
T39A4 000:796.351 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:796.361 - 0.013ms returns 0
T39A4 000:796.371 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:796.381 - 0.013ms returns 0
T39A4 000:796.391 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:796.400 - 0.013ms returns 0
T39A4 000:796.411 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:796.420 - 0.013ms returns 0
T39A4 000:796.431 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:796.442 - 0.015ms returns 0x00000016
T39A4 000:796.453 JLINK_Go()
T39A4 000:796.474   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:800.421 - 3.993ms
T39A4 000:800.466 JLINK_IsHalted()
T39A4 000:804.078   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:804.824 - 4.389ms returns TRUE
T39A4 000:804.877 JLINK_ReadReg(R15 (PC))
T39A4 000:804.896 - 0.024ms returns 0x20000000
T39A4 000:804.911 JLINK_ClrBPEx(BPHandle = 0x00000016)
T39A4 000:804.923 - 0.018ms returns 0x00
T39A4 000:804.941 JLINK_ReadReg(R0)
T39A4 000:804.952 - 0.017ms returns 0x00000001
T39A4 000:804.969 JLINK_HasError()
T39A4 000:804.983 JLINK_WriteReg(R0, 0x08002800)
T39A4 000:804.996 - 0.028ms returns 0
T39A4 000:805.020 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:805.032 - 0.016ms returns 0
T39A4 000:805.047 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:805.058 - 0.016ms returns 0
T39A4 000:805.072 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:805.083 - 0.016ms returns 0
T39A4 000:805.099 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:805.110 - 0.016ms returns 0
T39A4 000:805.125 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:805.136 - 0.016ms returns 0
T39A4 000:805.152 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:805.163 - 0.016ms returns 0
T39A4 000:805.179 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:805.190 - 0.016ms returns 0
T39A4 000:805.205 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:805.217 - 0.016ms returns 0
T39A4 000:805.232 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:805.243 - 0.016ms returns 0
T39A4 000:805.258 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:805.269 - 0.016ms returns 0
T39A4 000:805.283 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:805.294 - 0.016ms returns 0
T39A4 000:805.308 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:805.319 - 0.016ms returns 0
T39A4 000:805.334 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:805.345 - 0.017ms returns 0
T39A4 000:805.359 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:805.370 - 0.016ms returns 0
T39A4 000:805.383 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:805.395 - 0.016ms returns 0
T39A4 000:805.410 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:805.421 - 0.016ms returns 0
T39A4 000:805.434 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:805.445 - 0.016ms returns 0
T39A4 000:805.459 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:805.470 - 0.016ms returns 0
T39A4 000:805.485 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:805.496 - 0.016ms returns 0
T39A4 000:805.510 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:805.522 - 0.018ms returns 0x00000017
T39A4 000:805.536 JLINK_Go()
T39A4 000:805.560   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:809.576 - 4.064ms
T39A4 000:809.619 JLINK_IsHalted()
T39A4 000:810.227 - 0.643ms returns FALSE
T39A4 000:810.280 JLINK_HasError()
T39A4 000:812.103 JLINK_IsHalted()
T39A4 000:812.851 - 0.776ms returns FALSE
T39A4 000:812.896 JLINK_HasError()
T39A4 000:814.188 JLINK_IsHalted()
T39A4 000:814.805 - 0.638ms returns FALSE
T39A4 000:814.841 JLINK_HasError()
T39A4 000:816.198 JLINK_IsHalted()
T39A4 000:816.964 - 0.793ms returns FALSE
T39A4 000:817.008 JLINK_HasError()
T39A4 000:818.838 JLINK_IsHalted()
T39A4 000:819.402 - 0.580ms returns FALSE
T39A4 000:819.433 JLINK_HasError()
T39A4 000:824.994 JLINK_IsHalted()
T39A4 000:825.722 - 0.753ms returns FALSE
T39A4 000:825.761 JLINK_HasError()
T39A4 000:828.108 JLINK_IsHalted()
T39A4 000:828.857 - 0.772ms returns FALSE
T39A4 000:828.894 JLINK_HasError()
T39A4 000:830.221 JLINK_IsHalted()
T39A4 000:831.003 - 0.810ms returns FALSE
T39A4 000:831.045 JLINK_HasError()
T39A4 000:833.115 JLINK_IsHalted()
T39A4 000:836.914   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:837.645 - 4.557ms returns TRUE
T39A4 000:837.694 JLINK_ReadReg(R15 (PC))
T39A4 000:837.713 - 0.025ms returns 0x20000000
T39A4 000:837.728 JLINK_ClrBPEx(BPHandle = 0x00000017)
T39A4 000:837.741 - 0.018ms returns 0x00
T39A4 000:837.758 JLINK_ReadReg(R0)
T39A4 000:837.773 - 0.020ms returns 0x00000000
T39A4 000:838.640 JLINK_HasError()
T39A4 000:838.668 JLINK_WriteReg(R0, 0x08002C00)
T39A4 000:838.683 - 0.020ms returns 0
T39A4 000:838.697 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:838.708 - 0.016ms returns 0
T39A4 000:838.722 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:838.733 - 0.016ms returns 0
T39A4 000:838.747 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:838.758 - 0.016ms returns 0
T39A4 000:838.771 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:838.782 - 0.016ms returns 0
T39A4 000:838.795 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:838.809 - 0.019ms returns 0
T39A4 000:838.822 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:838.834 - 0.016ms returns 0
T39A4 000:838.847 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:838.858 - 0.016ms returns 0
T39A4 000:838.871 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:838.882 - 0.016ms returns 0
T39A4 000:838.895 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:838.906 - 0.016ms returns 0
T39A4 000:838.920 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:838.931 - 0.016ms returns 0
T39A4 000:838.944 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:838.955 - 0.016ms returns 0
T39A4 000:838.969 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:838.980 - 0.016ms returns 0
T39A4 000:838.993 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:839.005 - 0.017ms returns 0
T39A4 000:839.018 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:839.029 - 0.016ms returns 0
T39A4 000:839.042 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:839.054 - 0.016ms returns 0
T39A4 000:839.067 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:839.078 - 0.016ms returns 0
T39A4 000:839.091 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:839.102 - 0.016ms returns 0
T39A4 000:839.116 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:839.127 - 0.016ms returns 0
T39A4 000:839.140 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:839.151 - 0.016ms returns 0
T39A4 000:839.164 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:839.177 - 0.018ms returns 0x00000018
T39A4 000:839.191 JLINK_Go()
T39A4 000:839.216   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:843.261 - 4.093ms
T39A4 000:843.299 JLINK_IsHalted()
T39A4 000:846.968   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:847.713 - 4.443ms returns TRUE
T39A4 000:847.759 JLINK_ReadReg(R15 (PC))
T39A4 000:847.778 - 0.024ms returns 0x20000000
T39A4 000:847.793 JLINK_ClrBPEx(BPHandle = 0x00000018)
T39A4 000:847.807 - 0.019ms returns 0x00
T39A4 000:847.822 JLINK_ReadReg(R0)
T39A4 000:847.834 - 0.018ms returns 0x00000001
T39A4 000:847.850 JLINK_HasError()
T39A4 000:847.866 JLINK_WriteReg(R0, 0x08002C00)
T39A4 000:847.880 - 0.019ms returns 0
T39A4 000:847.894 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:847.905 - 0.016ms returns 0
T39A4 000:847.919 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:847.930 - 0.016ms returns 0
T39A4 000:847.943 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:847.960 - 0.025ms returns 0
T39A4 000:847.978 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:847.989 - 0.016ms returns 0
T39A4 000:848.002 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:848.014 - 0.016ms returns 0
T39A4 000:848.027 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:848.038 - 0.016ms returns 0
T39A4 000:848.051 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:848.062 - 0.016ms returns 0
T39A4 000:848.076 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:848.087 - 0.016ms returns 0
T39A4 000:848.100 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:848.111 - 0.016ms returns 0
T39A4 000:848.125 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:848.136 - 0.016ms returns 0
T39A4 000:848.149 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:848.160 - 0.016ms returns 0
T39A4 000:848.173 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:848.185 - 0.016ms returns 0
T39A4 000:848.198 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:848.210 - 0.017ms returns 0
T39A4 000:848.223 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:848.234 - 0.016ms returns 0
T39A4 000:848.247 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:848.259 - 0.016ms returns 0
T39A4 000:848.272 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:848.283 - 0.016ms returns 0
T39A4 000:848.296 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:848.308 - 0.016ms returns 0
T39A4 000:848.321 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:848.332 - 0.016ms returns 0
T39A4 000:848.345 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:848.357 - 0.016ms returns 0
T39A4 000:848.370 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:848.383 - 0.018ms returns 0x00000019
T39A4 000:848.397 JLINK_Go()
T39A4 000:848.420   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:852.272 - 3.901ms
T39A4 000:852.315 JLINK_IsHalted()
T39A4 000:852.890 - 0.592ms returns FALSE
T39A4 000:852.921 JLINK_HasError()
T39A4 000:856.104 JLINK_IsHalted()
T39A4 000:856.858 - 0.780ms returns FALSE
T39A4 000:856.902 JLINK_HasError()
T39A4 000:858.995 JLINK_IsHalted()
T39A4 000:859.581 - 0.609ms returns FALSE
T39A4 000:859.620 JLINK_HasError()
T39A4 000:861.485 JLINK_IsHalted()
T39A4 000:862.293 - 0.835ms returns FALSE
T39A4 000:862.337 JLINK_HasError()
T39A4 000:863.796 JLINK_IsHalted()
T39A4 000:864.618 - 0.850ms returns FALSE
T39A4 000:864.663 JLINK_HasError()
T39A4 000:866.469 JLINK_IsHalted()
T39A4 000:867.108 - 0.654ms returns FALSE
T39A4 000:867.137 JLINK_HasError()
T39A4 000:868.770 JLINK_IsHalted()
T39A4 000:869.425 - 0.683ms returns FALSE
T39A4 000:869.470 JLINK_HasError()
T39A4 000:870.742 JLINK_IsHalted()
T39A4 000:871.526 - 0.812ms returns FALSE
T39A4 000:871.572 JLINK_HasError()
T39A4 000:873.712 JLINK_IsHalted()
T39A4 000:877.539   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:878.295 - 4.627ms returns TRUE
T39A4 000:878.360 JLINK_ReadReg(R15 (PC))
T39A4 000:878.380 - 0.025ms returns 0x20000000
T39A4 000:878.394 JLINK_ClrBPEx(BPHandle = 0x00000019)
T39A4 000:878.407 - 0.018ms returns 0x00
T39A4 000:878.422 JLINK_ReadReg(R0)
T39A4 000:878.433 - 0.017ms returns 0x00000000
T39A4 000:879.296 JLINK_HasError()
T39A4 000:879.323 JLINK_WriteReg(R0, 0x08003000)
T39A4 000:879.338 - 0.020ms returns 0
T39A4 000:879.352 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:879.364 - 0.016ms returns 0
T39A4 000:879.377 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:879.388 - 0.019ms returns 0
T39A4 000:879.404 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:879.415 - 0.016ms returns 0
T39A4 000:879.429 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:879.440 - 0.016ms returns 0
T39A4 000:879.453 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:879.464 - 0.016ms returns 0
T39A4 000:879.478 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:879.489 - 0.016ms returns 0
T39A4 000:879.502 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:879.513 - 0.016ms returns 0
T39A4 000:879.526 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:879.538 - 0.016ms returns 0
T39A4 000:879.551 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:879.562 - 0.016ms returns 0
T39A4 000:879.575 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:879.586 - 0.016ms returns 0
T39A4 000:879.600 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:879.619 - 0.025ms returns 0
T39A4 000:879.633 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:879.644 - 0.016ms returns 0
T39A4 000:879.657 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:879.669 - 0.017ms returns 0
T39A4 000:879.682 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:879.694 - 0.016ms returns 0
T39A4 000:879.707 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:879.718 - 0.016ms returns 0
T39A4 000:879.731 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:879.743 - 0.016ms returns 0
T39A4 000:879.756 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:879.767 - 0.016ms returns 0
T39A4 000:879.780 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:879.791 - 0.016ms returns 0
T39A4 000:879.805 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:879.816 - 0.016ms returns 0
T39A4 000:879.829 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:879.842 - 0.018ms returns 0x0000001A
T39A4 000:879.856 JLINK_Go()
T39A4 000:879.880   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:884.057 - 4.220ms
T39A4 000:884.088 JLINK_IsHalted()
T39A4 000:887.329   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:887.893 - 3.820ms returns TRUE
T39A4 000:887.921 JLINK_ReadReg(R15 (PC))
T39A4 000:887.938 - 0.022ms returns 0x20000000
T39A4 000:887.953 JLINK_ClrBPEx(BPHandle = 0x0000001A)
T39A4 000:887.965 - 0.018ms returns 0x00
T39A4 000:887.980 JLINK_ReadReg(R0)
T39A4 000:887.992 - 0.017ms returns 0x00000001
T39A4 000:888.007 JLINK_HasError()
T39A4 000:888.021 JLINK_WriteReg(R0, 0x08003000)
T39A4 000:888.034 - 0.018ms returns 0
T39A4 000:888.048 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:888.059 - 0.017ms returns 0
T39A4 000:888.073 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:888.085 - 0.017ms returns 0
T39A4 000:888.098 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:888.110 - 0.017ms returns 0
T39A4 000:888.124 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:888.135 - 0.017ms returns 0
T39A4 000:888.149 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:888.161 - 0.017ms returns 0
T39A4 000:888.175 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:888.186 - 0.017ms returns 0
T39A4 000:888.200 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:888.211 - 0.017ms returns 0
T39A4 000:888.225 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:888.237 - 0.017ms returns 0
T39A4 000:888.250 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:888.262 - 0.016ms returns 0
T39A4 000:888.276 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:888.287 - 0.016ms returns 0
T39A4 000:888.300 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:888.312 - 0.017ms returns 0
T39A4 000:888.326 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:888.337 - 0.016ms returns 0
T39A4 000:888.351 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:888.363 - 0.019ms returns 0
T39A4 000:888.379 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:888.390 - 0.017ms returns 0
T39A4 000:888.404 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:888.415 - 0.017ms returns 0
T39A4 000:888.429 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:888.441 - 0.017ms returns 0
T39A4 000:888.454 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:888.466 - 0.017ms returns 0
T39A4 000:888.480 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:888.491 - 0.017ms returns 0
T39A4 000:888.505 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:888.517 - 0.017ms returns 0
T39A4 000:888.530 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:888.543 - 0.018ms returns 0x0000001B
T39A4 000:888.557 JLINK_Go()
T39A4 000:888.579   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:892.539 - 4.006ms
T39A4 000:892.583 JLINK_IsHalted()
T39A4 000:893.141 - 0.572ms returns FALSE
T39A4 000:893.172 JLINK_HasError()
T39A4 000:894.701 JLINK_IsHalted()
T39A4 000:895.452 - 0.779ms returns FALSE
T39A4 000:895.497 JLINK_HasError()
T39A4 000:896.703 JLINK_IsHalted()
T39A4 000:897.326 - 0.641ms returns FALSE
T39A4 000:897.359 JLINK_HasError()
T39A4 000:898.670 JLINK_IsHalted()
T39A4 000:899.420 - 0.779ms returns FALSE
T39A4 000:899.476 JLINK_HasError()
T39A4 000:900.647 JLINK_IsHalted()
T39A4 000:901.402 - 0.784ms returns FALSE
T39A4 000:901.447 JLINK_HasError()
T39A4 000:903.624 JLINK_IsHalted()
T39A4 000:904.365 - 0.768ms returns FALSE
T39A4 000:904.409 JLINK_HasError()
T39A4 000:905.695 JLINK_IsHalted()
T39A4 000:906.442 - 0.774ms returns FALSE
T39A4 000:906.486 JLINK_HasError()
T39A4 000:908.597 JLINK_IsHalted()
T39A4 000:909.174 - 0.597ms returns FALSE
T39A4 000:909.210 JLINK_HasError()
T39A4 000:910.398 JLINK_IsHalted()
T39A4 000:910.944 - 0.560ms returns FALSE
T39A4 000:910.971 JLINK_HasError()
T39A4 000:912.663 JLINK_IsHalted()
T39A4 000:913.221 - 0.574ms returns FALSE
T39A4 000:913.252 JLINK_HasError()
T39A4 000:917.671 JLINK_IsHalted()
T39A4 000:921.327   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:922.118 - 4.470ms returns TRUE
T39A4 000:922.159 JLINK_ReadReg(R15 (PC))
T39A4 000:922.175 - 0.020ms returns 0x20000000
T39A4 000:922.209 JLINK_ClrBPEx(BPHandle = 0x0000001B)
T39A4 000:922.221 - 0.017ms returns 0x00
T39A4 000:922.237 JLINK_ReadReg(R0)
T39A4 000:922.250 - 0.036ms returns 0x00000000
T39A4 000:923.064 JLINK_HasError()
T39A4 000:923.088 JLINK_WriteReg(R0, 0x08003400)
T39A4 000:923.102 - 0.019ms returns 0
T39A4 000:923.115 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:923.125 - 0.015ms returns 0
T39A4 000:923.137 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:923.147 - 0.015ms returns 0
T39A4 000:923.160 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:923.170 - 0.015ms returns 0
T39A4 000:923.182 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:923.192 - 0.014ms returns 0
T39A4 000:923.204 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:923.214 - 0.015ms returns 0
T39A4 000:923.227 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:923.237 - 0.015ms returns 0
T39A4 000:923.249 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:923.259 - 0.015ms returns 0
T39A4 000:923.271 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:923.284 - 0.018ms returns 0
T39A4 000:923.296 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:923.307 - 0.015ms returns 0
T39A4 000:923.319 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:923.329 - 0.015ms returns 0
T39A4 000:923.341 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:923.351 - 0.015ms returns 0
T39A4 000:923.364 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:923.374 - 0.014ms returns 0
T39A4 000:923.386 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:923.397 - 0.015ms returns 0
T39A4 000:923.409 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:923.419 - 0.015ms returns 0
T39A4 000:923.431 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:923.442 - 0.015ms returns 0
T39A4 000:923.454 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:923.464 - 0.015ms returns 0
T39A4 000:923.476 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:923.486 - 0.015ms returns 0
T39A4 000:923.498 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:923.509 - 0.015ms returns 0
T39A4 000:923.521 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:923.531 - 0.014ms returns 0
T39A4 000:923.543 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:923.555 - 0.016ms returns 0x0000001C
T39A4 000:923.568 JLINK_Go()
T39A4 000:923.590   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:927.755 - 4.214ms
T39A4 000:927.799 JLINK_IsHalted()
T39A4 000:931.273   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:931.783 - 3.994ms returns TRUE
T39A4 000:931.804 JLINK_ReadReg(R15 (PC))
T39A4 000:931.816 - 0.016ms returns 0x20000000
T39A4 000:931.827 JLINK_ClrBPEx(BPHandle = 0x0000001C)
T39A4 000:931.836 - 0.013ms returns 0x00
T39A4 000:931.847 JLINK_ReadReg(R0)
T39A4 000:931.856 - 0.012ms returns 0x00000001
T39A4 000:931.867 JLINK_HasError()
T39A4 000:931.877 JLINK_WriteReg(R0, 0x08003400)
T39A4 000:931.886 - 0.013ms returns 0
T39A4 000:931.897 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:931.905 - 0.012ms returns 0
T39A4 000:931.915 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:931.924 - 0.012ms returns 0
T39A4 000:931.934 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:931.942 - 0.012ms returns 0
T39A4 000:931.952 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:931.961 - 0.012ms returns 0
T39A4 000:931.971 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:931.979 - 0.012ms returns 0
T39A4 000:931.989 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:931.998 - 0.016ms returns 0
T39A4 000:932.015 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:932.023 - 0.012ms returns 0
T39A4 000:932.033 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:932.042 - 0.012ms returns 0
T39A4 000:932.052 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:932.060 - 0.012ms returns 0
T39A4 000:932.070 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:932.079 - 0.012ms returns 0
T39A4 000:932.088 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:932.097 - 0.012ms returns 0
T39A4 000:932.107 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:932.115 - 0.012ms returns 0
T39A4 000:932.125 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:932.134 - 0.012ms returns 0
T39A4 000:932.144 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:932.152 - 0.012ms returns 0
T39A4 000:932.162 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:932.171 - 0.012ms returns 0
T39A4 000:932.181 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:932.189 - 0.012ms returns 0
T39A4 000:932.199 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:932.208 - 0.014ms returns 0
T39A4 000:932.219 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:932.228 - 0.012ms returns 0
T39A4 000:932.238 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:932.246 - 0.012ms returns 0
T39A4 000:932.256 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:932.266 - 0.013ms returns 0x0000001D
T39A4 000:932.276 JLINK_Go()
T39A4 000:932.293   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:936.236 - 3.970ms
T39A4 000:936.255 JLINK_IsHalted()
T39A4 000:936.765 - 0.519ms returns FALSE
T39A4 000:936.784 JLINK_HasError()
T39A4 000:943.320 JLINK_IsHalted()
T39A4 000:944.001 - 0.690ms returns FALSE
T39A4 000:944.020 JLINK_HasError()
T39A4 000:945.654 JLINK_IsHalted()
T39A4 000:946.430 - 0.799ms returns FALSE
T39A4 000:946.465 JLINK_HasError()
T39A4 000:948.554 JLINK_IsHalted()
T39A4 000:949.322 - 0.796ms returns FALSE
T39A4 000:949.365 JLINK_HasError()
T39A4 000:950.591 JLINK_IsHalted()
T39A4 000:951.138 - 0.562ms returns FALSE
T39A4 000:951.167 JLINK_HasError()
T39A4 000:952.523 JLINK_IsHalted()
T39A4 000:953.308 - 0.810ms returns FALSE
T39A4 000:953.348 JLINK_HasError()
T39A4 000:954.560 JLINK_IsHalted()
T39A4 000:955.316 - 0.782ms returns FALSE
T39A4 000:955.357 JLINK_HasError()
T39A4 000:956.517 JLINK_IsHalted()
T39A4 000:957.331 - 0.841ms returns FALSE
T39A4 000:957.374 JLINK_HasError()
T39A4 000:959.178 JLINK_IsHalted()
T39A4 000:962.594   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:963.139 - 3.979ms returns TRUE
T39A4 000:963.173 JLINK_ReadReg(R15 (PC))
T39A4 000:963.191 - 0.024ms returns 0x20000000
T39A4 000:963.207 JLINK_ClrBPEx(BPHandle = 0x0000001D)
T39A4 000:963.219 - 0.018ms returns 0x00
T39A4 000:963.234 JLINK_ReadReg(R0)
T39A4 000:963.246 - 0.017ms returns 0x00000000
T39A4 000:964.112 JLINK_HasError()
T39A4 000:964.139 JLINK_WriteReg(R0, 0x08003800)
T39A4 000:964.153 - 0.020ms returns 0
T39A4 000:964.168 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:964.179 - 0.017ms returns 0
T39A4 000:964.195 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:964.212 - 0.023ms returns 0
T39A4 000:964.226 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:964.241 - 0.020ms returns 0
T39A4 000:964.255 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:964.266 - 0.016ms returns 0
T39A4 000:964.279 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:964.291 - 0.016ms returns 0
T39A4 000:964.304 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:964.315 - 0.016ms returns 0
T39A4 000:964.328 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:964.339 - 0.016ms returns 0
T39A4 000:964.353 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:964.364 - 0.027ms returns 0
T39A4 000:964.389 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:964.400 - 0.016ms returns 0
T39A4 000:964.413 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:964.424 - 0.016ms returns 0
T39A4 000:964.438 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:964.449 - 0.016ms returns 0
T39A4 000:964.462 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:964.473 - 0.016ms returns 0
T39A4 000:964.486 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:964.498 - 0.017ms returns 0
T39A4 000:964.512 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:964.527 - 0.024ms returns 0
T39A4 000:964.545 JLINK_WriteReg(R15 (PC), 0x20000020)
T39A4 000:964.556 - 0.016ms returns 0
T39A4 000:964.569 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:964.581 - 0.016ms returns 0
T39A4 000:964.594 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:964.605 - 0.016ms returns 0
T39A4 000:964.618 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:964.629 - 0.016ms returns 0
T39A4 000:964.643 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:964.654 - 0.016ms returns 0
T39A4 000:964.667 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:964.680 - 0.018ms returns 0x0000001E
T39A4 000:964.694 JLINK_Go()
T39A4 000:964.718   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:968.735 - 4.067ms
T39A4 000:968.778 JLINK_IsHalted()
T39A4 000:972.150   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 000:972.899 - 4.148ms returns TRUE
T39A4 000:972.943 JLINK_ReadReg(R15 (PC))
T39A4 000:972.961 - 0.024ms returns 0x20000000
T39A4 000:972.976 JLINK_ClrBPEx(BPHandle = 0x0000001E)
T39A4 000:972.988 - 0.018ms returns 0x00
T39A4 000:973.003 JLINK_ReadReg(R0)
T39A4 000:973.015 - 0.017ms returns 0x00000001
T39A4 000:973.030 JLINK_HasError()
T39A4 000:973.045 JLINK_WriteReg(R0, 0x08003800)
T39A4 000:973.057 - 0.018ms returns 0
T39A4 000:973.071 JLINK_WriteReg(R1, 0x00000400)
T39A4 000:973.083 - 0.017ms returns 0
T39A4 000:973.096 JLINK_WriteReg(R2, 0x000000FF)
T39A4 000:973.108 - 0.017ms returns 0
T39A4 000:973.122 JLINK_WriteReg(R3, 0x00000000)
T39A4 000:973.133 - 0.020ms returns 0
T39A4 000:973.151 JLINK_WriteReg(R4, 0x00000000)
T39A4 000:973.163 - 0.017ms returns 0
T39A4 000:973.177 JLINK_WriteReg(R5, 0x00000000)
T39A4 000:973.188 - 0.016ms returns 0
T39A4 000:973.202 JLINK_WriteReg(R6, 0x00000000)
T39A4 000:973.213 - 0.016ms returns 0
T39A4 000:973.231 JLINK_WriteReg(R7, 0x00000000)
T39A4 000:973.242 - 0.016ms returns 0
T39A4 000:973.256 JLINK_WriteReg(R8, 0x00000000)
T39A4 000:973.267 - 0.017ms returns 0
T39A4 000:973.281 JLINK_WriteReg(R9, 0x20000160)
T39A4 000:973.292 - 0.016ms returns 0
T39A4 000:973.308 JLINK_WriteReg(R10, 0x00000000)
T39A4 000:973.319 - 0.016ms returns 0
T39A4 000:973.332 JLINK_WriteReg(R11, 0x00000000)
T39A4 000:973.344 - 0.016ms returns 0
T39A4 000:973.357 JLINK_WriteReg(R12, 0x00000000)
T39A4 000:973.368 - 0.016ms returns 0
T39A4 000:973.383 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 000:973.395 - 0.017ms returns 0
T39A4 000:973.408 JLINK_WriteReg(R14, 0x20000001)
T39A4 000:973.420 - 0.016ms returns 0
T39A4 000:973.433 JLINK_WriteReg(R15 (PC), 0x200000B6)
T39A4 000:973.444 - 0.016ms returns 0
T39A4 000:973.459 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 000:973.471 - 0.016ms returns 0
T39A4 000:973.484 JLINK_WriteReg(MSP, 0x20001000)
T39A4 000:973.495 - 0.016ms returns 0
T39A4 000:973.615 JLINK_WriteReg(PSP, 0x20001000)
T39A4 000:973.642 - 0.033ms returns 0
T39A4 000:973.657 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 000:973.670 - 0.018ms returns 0
T39A4 000:973.685 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 000:973.699 - 0.019ms returns 0x0000001F
T39A4 000:973.713 JLINK_Go()
T39A4 000:973.738   CPU_ReadMem(4 bytes @ 0x********)
T39A4 000:977.615 - 3.925ms
T39A4 000:977.654 JLINK_IsHalted()
T39A4 000:978.140 - 0.508ms returns FALSE
T39A4 000:978.181 JLINK_HasError()
T39A4 000:984.124 JLINK_IsHalted()
T39A4 000:984.591 - 0.475ms returns FALSE
T39A4 000:984.608 JLINK_HasError()
T39A4 000:986.137 JLINK_IsHalted()
T39A4 000:986.630 - 0.502ms returns FALSE
T39A4 000:986.649 JLINK_HasError()
T39A4 000:988.162 JLINK_IsHalted()
T39A4 000:988.697 - 0.544ms returns FALSE
T39A4 000:988.716 JLINK_HasError()
T39A4 000:990.163 JLINK_IsHalted()
T39A4 000:990.689 - 0.535ms returns FALSE
T39A4 000:990.708 JLINK_HasError()
T39A4 000:992.142 JLINK_IsHalted()
T39A4 000:992.620 - 0.490ms returns FALSE
T39A4 000:992.642 JLINK_HasError()
T39A4 000:994.133 JLINK_IsHalted()
T39A4 000:994.876 - 0.764ms returns FALSE
T39A4 000:994.909 JLINK_HasError()
T39A4 000:996.442 JLINK_IsHalted()
T39A4 000:997.228 - 0.809ms returns FALSE
T39A4 000:997.268 JLINK_HasError()
T39A4 000:998.401 JLINK_IsHalted()
T39A4 000:999.204 - 0.829ms returns FALSE
T39A4 000:999.246 JLINK_HasError()
T39A4 001:000.401 JLINK_IsHalted()
T39A4 001:004.270   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:005.096 - 4.719ms returns TRUE
T39A4 001:005.133 JLINK_ReadReg(R15 (PC))
T39A4 001:005.147 - 0.018ms returns 0x20000000
T39A4 001:005.158 JLINK_ClrBPEx(BPHandle = 0x0000001F)
T39A4 001:005.167 - 0.013ms returns 0x00
T39A4 001:005.178 JLINK_ReadReg(R0)
T39A4 001:005.186 - 0.012ms returns 0x00000000
T39A4 001:005.646 JLINK_HasError()
T39A4 001:005.670 JLINK_WriteReg(R0, 0x00000001)
T39A4 001:005.682 - 0.016ms returns 0
T39A4 001:005.692 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:005.700 - 0.012ms returns 0
T39A4 001:005.710 JLINK_WriteReg(R2, 0x000000FF)
T39A4 001:005.718 - 0.011ms returns 0
T39A4 001:005.727 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:005.735 - 0.011ms returns 0
T39A4 001:005.745 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:005.752 - 0.011ms returns 0
T39A4 001:005.762 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:005.771 - 0.012ms returns 0
T39A4 001:005.780 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:005.789 - 0.013ms returns 0
T39A4 001:005.800 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:005.809 - 0.013ms returns 0
T39A4 001:005.820 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:005.830 - 0.014ms returns 0
T39A4 001:005.841 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:005.849 - 0.011ms returns 0
T39A4 001:005.858 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:005.867 - 0.012ms returns 0
T39A4 001:005.876 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:005.885 - 0.013ms returns 0
T39A4 001:005.896 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:005.904 - 0.011ms returns 0
T39A4 001:005.913 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:005.922 - 0.013ms returns 0
T39A4 001:005.932 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:005.940 - 0.011ms returns 0
T39A4 001:005.949 JLINK_WriteReg(R15 (PC), 0x2000006A)
T39A4 001:005.957 - 0.011ms returns 0
T39A4 001:005.967 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:005.975 - 0.011ms returns 0
T39A4 001:005.984 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:005.992 - 0.011ms returns 0
T39A4 001:006.002 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:006.009 - 0.011ms returns 0
T39A4 001:006.021 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:006.029 - 0.011ms returns 0
T39A4 001:006.039 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:006.048 - 0.013ms returns 0x00000020
T39A4 001:006.058 JLINK_Go()
T39A4 001:006.080   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:010.142 - 4.103ms
T39A4 001:010.173 JLINK_IsHalted()
T39A4 001:013.515   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:014.165 - 4.002ms returns TRUE
T39A4 001:014.185 JLINK_ReadReg(R15 (PC))
T39A4 001:014.197 - 0.016ms returns 0x20000000
T39A4 001:014.207 JLINK_ClrBPEx(BPHandle = 0x00000020)
T39A4 001:014.216 - 0.013ms returns 0x00
T39A4 001:014.227 JLINK_ReadReg(R0)
T39A4 001:014.235 - 0.012ms returns 0x00000000
T39A4 001:076.222 JLINK_WriteMem(0x20000000, 0x164 Bytes, ...)
T39A4 001:076.244   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T39A4 001:076.269   CPU_WriteMem(356 bytes @ 0x20000000)
T39A4 001:081.022 - 4.811ms returns 0x164
T39A4 001:081.067 JLINK_HasError()
T39A4 001:081.082 JLINK_WriteReg(R0, 0x08000000)
T39A4 001:081.095 - 0.017ms returns 0
T39A4 001:081.106 JLINK_WriteReg(R1, 0x007A1200)
T39A4 001:081.115 - 0.014ms returns 0
T39A4 001:081.126 JLINK_WriteReg(R2, 0x00000002)
T39A4 001:081.136 - 0.013ms returns 0
T39A4 001:081.146 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:081.155 - 0.013ms returns 0
T39A4 001:081.166 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:081.175 - 0.013ms returns 0
T39A4 001:081.186 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:081.195 - 0.013ms returns 0
T39A4 001:081.206 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:081.215 - 0.013ms returns 0
T39A4 001:081.226 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:081.235 - 0.013ms returns 0
T39A4 001:081.245 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:081.255 - 0.017ms returns 0
T39A4 001:081.273 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:081.282 - 0.013ms returns 0
T39A4 001:081.293 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:081.302 - 0.013ms returns 0
T39A4 001:081.313 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:081.322 - 0.013ms returns 0
T39A4 001:081.332 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:081.341 - 0.013ms returns 0
T39A4 001:081.352 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:081.362 - 0.014ms returns 0
T39A4 001:081.372 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:081.382 - 0.013ms returns 0
T39A4 001:081.392 JLINK_WriteReg(R15 (PC), 0x20000038)
T39A4 001:081.401 - 0.013ms returns 0
T39A4 001:081.412 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:081.421 - 0.013ms returns 0
T39A4 001:081.432 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:081.441 - 0.013ms returns 0
T39A4 001:081.451 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:081.460 - 0.013ms returns 0
T39A4 001:081.471 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:081.480 - 0.013ms returns 0
T39A4 001:081.491 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:081.505   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:082.091 - 0.620ms returns 0x00000021
T39A4 001:082.123 JLINK_Go()
T39A4 001:082.139   CPU_WriteMem(2 bytes @ 0x20000000)
T39A4 001:082.965   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:086.979 - 4.879ms
T39A4 001:087.017 JLINK_IsHalted()
T39A4 001:090.354   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:090.833 - 3.825ms returns TRUE
T39A4 001:090.852 JLINK_ReadReg(R15 (PC))
T39A4 001:090.863 - 0.015ms returns 0x20000000
T39A4 001:090.874 JLINK_ClrBPEx(BPHandle = 0x00000021)
T39A4 001:090.882 - 0.012ms returns 0x00
T39A4 001:090.892 JLINK_ReadReg(R0)
T39A4 001:090.900 - 0.011ms returns 0x00000000
T39A4 001:092.580 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:092.600   Data:  E8 05 00 20 01 01 00 08 13 2E 00 08 0F 1D 00 08 ...
T39A4 001:092.626   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:104.908 - 12.343ms returns 0x400
T39A4 001:104.938 JLINK_HasError()
T39A4 001:104.951 JLINK_WriteReg(R0, 0x08000000)
T39A4 001:104.967 - 0.019ms returns 0
T39A4 001:104.977 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:104.986 - 0.011ms returns 0
T39A4 001:104.996 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:105.003 - 0.010ms returns 0
T39A4 001:105.013 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:105.020 - 0.010ms returns 0
T39A4 001:105.031 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:105.038 - 0.010ms returns 0
T39A4 001:105.047 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:105.055 - 0.010ms returns 0
T39A4 001:105.065 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:105.072 - 0.010ms returns 0
T39A4 001:105.082 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:105.089 - 0.010ms returns 0
T39A4 001:105.100 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:105.107 - 0.010ms returns 0
T39A4 001:105.117 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:105.124 - 0.011ms returns 0
T39A4 001:105.137 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:105.147 - 0.013ms returns 0
T39A4 001:105.157 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:105.164 - 0.010ms returns 0
T39A4 001:105.174 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:105.181 - 0.010ms returns 0
T39A4 001:105.191 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:105.199 - 0.011ms returns 0
T39A4 001:105.209 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:105.216 - 0.010ms returns 0
T39A4 001:105.226 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:105.233 - 0.010ms returns 0
T39A4 001:105.243 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:105.251 - 0.010ms returns 0
T39A4 001:105.261 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:105.268 - 0.010ms returns 0
T39A4 001:105.278 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:105.285 - 0.010ms returns 0
T39A4 001:105.295 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:105.302 - 0.010ms returns 0
T39A4 001:105.312 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:105.322 - 0.014ms returns 0x00000022
T39A4 001:105.336 JLINK_Go()
T39A4 001:105.360   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:108.768 - 3.450ms
T39A4 001:108.804 JLINK_IsHalted()
T39A4 001:109.212 - 0.420ms returns FALSE
T39A4 001:109.237 JLINK_HasError()
T39A4 001:124.576 JLINK_IsHalted()
T39A4 001:125.067 - 0.507ms returns FALSE
T39A4 001:125.098 JLINK_HasError()
T39A4 001:126.562 JLINK_IsHalted()
T39A4 001:127.033 - 0.489ms returns FALSE
T39A4 001:127.070 JLINK_HasError()
T39A4 001:128.557 JLINK_IsHalted()
T39A4 001:129.030 - 0.481ms returns FALSE
T39A4 001:129.048 JLINK_HasError()
T39A4 001:130.549 JLINK_IsHalted()
T39A4 001:131.042 - 0.509ms returns FALSE
T39A4 001:131.073 JLINK_HasError()
T39A4 001:132.566 JLINK_IsHalted()
T39A4 001:133.072 - 0.530ms returns FALSE
T39A4 001:133.117 JLINK_HasError()
T39A4 001:134.549 JLINK_IsHalted()
T39A4 001:135.043 - 0.511ms returns FALSE
T39A4 001:135.075 JLINK_HasError()
T39A4 001:136.549 JLINK_IsHalted()
T39A4 001:137.044 - 0.515ms returns FALSE
T39A4 001:137.078 JLINK_HasError()
T39A4 001:138.549 JLINK_IsHalted()
T39A4 001:139.116 - 0.614ms returns FALSE
T39A4 001:139.184 JLINK_HasError()
T39A4 001:145.526 JLINK_IsHalted()
T39A4 001:148.806   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:149.303 - 3.792ms returns TRUE
T39A4 001:149.334 JLINK_ReadReg(R15 (PC))
T39A4 001:149.354 - 0.026ms returns 0x20000000
T39A4 001:149.372 JLINK_ClrBPEx(BPHandle = 0x00000022)
T39A4 001:149.387 - 0.021ms returns 0x00
T39A4 001:149.403 JLINK_ReadReg(R0)
T39A4 001:149.417 - 0.020ms returns 0x00000000
T39A4 001:150.479 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:150.518   Data:  90 F8 20 40 01 2C 05 D0 02 2C 03 D0 41 6A 41 F4 ...
T39A4 001:150.562   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:162.846 - 12.407ms returns 0x400
T39A4 001:162.912 JLINK_HasError()
T39A4 001:162.933 JLINK_WriteReg(R0, 0x08000400)
T39A4 001:162.961 - 0.034ms returns 0
T39A4 001:162.979 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:162.992 - 0.018ms returns 0
T39A4 001:163.009 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:163.021 - 0.018ms returns 0
T39A4 001:163.039 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:163.052 - 0.018ms returns 0
T39A4 001:163.070 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:163.085 - 0.021ms returns 0
T39A4 001:163.103 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:163.115 - 0.018ms returns 0
T39A4 001:163.133 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:163.146 - 0.019ms returns 0
T39A4 001:163.163 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:163.175 - 0.018ms returns 0
T39A4 001:163.192 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:163.205 - 0.019ms returns 0
T39A4 001:163.221 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:163.237 - 0.022ms returns 0
T39A4 001:163.256 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:163.269 - 0.018ms returns 0
T39A4 001:163.286 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:163.298 - 0.019ms returns 0
T39A4 001:163.317 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:163.328 - 0.017ms returns 0
T39A4 001:163.345 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:163.358 - 0.018ms returns 0
T39A4 001:163.374 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:163.386 - 0.018ms returns 0
T39A4 001:163.405 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:163.417 - 0.018ms returns 0
T39A4 001:163.434 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:163.449 - 0.022ms returns 0
T39A4 001:163.467 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:163.478 - 0.016ms returns 0
T39A4 001:163.495 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:163.506 - 0.017ms returns 0
T39A4 001:163.523 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:163.536 - 0.019ms returns 0
T39A4 001:163.556 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:163.574 - 0.024ms returns 0x00000023
T39A4 001:163.593 JLINK_Go()
T39A4 001:163.629   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:167.267 - 3.709ms
T39A4 001:167.328 JLINK_IsHalted()
T39A4 001:167.751 - 0.436ms returns FALSE
T39A4 001:167.781 JLINK_HasError()
T39A4 001:173.482 JLINK_IsHalted()
T39A4 001:174.036 - 0.574ms returns FALSE
T39A4 001:174.071 JLINK_HasError()
T39A4 001:175.433 JLINK_IsHalted()
T39A4 001:175.941 - 0.529ms returns FALSE
T39A4 001:175.977 JLINK_HasError()
T39A4 001:177.555 JLINK_IsHalted()
T39A4 001:178.455 - 0.956ms returns FALSE
T39A4 001:178.538 JLINK_HasError()
T39A4 001:201.378 JLINK_IsHalted()
T39A4 001:204.667   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:205.148 - 3.786ms returns TRUE
T39A4 001:205.179 JLINK_ReadReg(R15 (PC))
T39A4 001:205.200 - 0.027ms returns 0x20000000
T39A4 001:205.217 JLINK_ClrBPEx(BPHandle = 0x00000023)
T39A4 001:205.232 - 0.021ms returns 0x00
T39A4 001:205.249 JLINK_ReadReg(R0)
T39A4 001:205.263 - 0.020ms returns 0x00000000
T39A4 001:206.218 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:206.248   Data:  7D D1 67 4E D1 F8 04 C0 AC EB 06 08 B4 45 32 D0 ...
T39A4 001:206.282   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:218.541 - 12.345ms returns 0x400
T39A4 001:218.579 JLINK_HasError()
T39A4 001:218.593 JLINK_WriteReg(R0, 0x08000800)
T39A4 001:218.612 - 0.023ms returns 0
T39A4 001:218.624 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:218.633 - 0.013ms returns 0
T39A4 001:218.643 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:218.651 - 0.012ms returns 0
T39A4 001:218.661 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:218.670 - 0.012ms returns 0
T39A4 001:218.680 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:218.692 - 0.017ms returns 0
T39A4 001:218.705 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:218.714 - 0.012ms returns 0
T39A4 001:218.724 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:218.735 - 0.015ms returns 0
T39A4 001:218.746 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:218.755 - 0.012ms returns 0
T39A4 001:218.766 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:218.776 - 0.014ms returns 0
T39A4 001:218.786 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:218.794 - 0.013ms returns 0
T39A4 001:218.808 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:218.816 - 0.012ms returns 0
T39A4 001:218.826 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:218.835 - 0.014ms returns 0
T39A4 001:218.849 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:218.857 - 0.012ms returns 0
T39A4 001:218.867 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:218.878 - 0.017ms returns 0
T39A4 001:218.891 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:218.900 - 0.012ms returns 0
T39A4 001:218.910 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:218.921 - 0.017ms returns 0
T39A4 001:218.933 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:218.942 - 0.012ms returns 0
T39A4 001:218.952 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:218.963 - 0.015ms returns 0
T39A4 001:218.974 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:218.983 - 0.015ms returns 0
T39A4 001:218.999 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:219.012 - 0.020ms returns 0
T39A4 001:219.027 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:219.042 - 0.020ms returns 0x00000024
T39A4 001:219.056 JLINK_Go()
T39A4 001:219.089   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:222.661 - 3.625ms
T39A4 001:222.709 JLINK_IsHalted()
T39A4 001:223.115 - 0.418ms returns FALSE
T39A4 001:223.144 JLINK_HasError()
T39A4 001:230.327 JLINK_IsHalted()
T39A4 001:230.800 - 0.486ms returns FALSE
T39A4 001:230.825 JLINK_HasError()
T39A4 001:232.306 JLINK_IsHalted()
T39A4 001:232.765 - 0.469ms returns FALSE
T39A4 001:232.785 JLINK_HasError()
T39A4 001:234.326 JLINK_IsHalted()
T39A4 001:234.793 - 0.479ms returns FALSE
T39A4 001:234.819 JLINK_HasError()
T39A4 001:236.269 JLINK_IsHalted()
T39A4 001:236.731 - 0.474ms returns FALSE
T39A4 001:236.758 JLINK_HasError()
T39A4 001:240.257 JLINK_IsHalted()
T39A4 001:240.691 - 0.445ms returns FALSE
T39A4 001:240.714 JLINK_HasError()
T39A4 001:242.280 JLINK_IsHalted()
T39A4 001:242.775 - 0.513ms returns FALSE
T39A4 001:242.809 JLINK_HasError()
T39A4 001:244.260 JLINK_IsHalted()
T39A4 001:244.764 - 0.518ms returns FALSE
T39A4 001:244.792 JLINK_HasError()
T39A4 001:250.251 JLINK_IsHalted()
T39A4 001:250.773 - 0.534ms returns FALSE
T39A4 001:250.796 JLINK_HasError()
T39A4 001:252.221 JLINK_IsHalted()
T39A4 001:252.689 - 0.478ms returns FALSE
T39A4 001:252.709 JLINK_HasError()
T39A4 001:254.214 JLINK_IsHalted()
T39A4 001:257.342   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:257.795 - 3.596ms returns TRUE
T39A4 001:257.823 JLINK_ReadReg(R15 (PC))
T39A4 001:257.835 - 0.016ms returns 0x20000000
T39A4 001:257.846 JLINK_ClrBPEx(BPHandle = 0x00000024)
T39A4 001:257.855 - 0.012ms returns 0x00
T39A4 001:257.865 JLINK_ReadReg(R0)
T39A4 001:257.873 - 0.012ms returns 0x00000000
T39A4 001:258.999 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:259.021   Data:  F0 47 01 F0 BF BD 28 21 21 2D 27 D0 29 2D 25 D0 ...
T39A4 001:259.052   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:271.318 - 12.365ms returns 0x400
T39A4 001:271.387 JLINK_HasError()
T39A4 001:271.412 JLINK_WriteReg(R0, 0x08000C00)
T39A4 001:271.438 - 0.032ms returns 0
T39A4 001:271.454 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:271.466 - 0.017ms returns 0
T39A4 001:271.480 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:271.494 - 0.019ms returns 0
T39A4 001:271.511 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:271.523 - 0.019ms returns 0
T39A4 001:271.539 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:271.552 - 0.018ms returns 0
T39A4 001:271.566 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:271.578 - 0.018ms returns 0
T39A4 001:271.593 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:271.606 - 0.018ms returns 0
T39A4 001:271.620 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:271.632 - 0.017ms returns 0
T39A4 001:271.646 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:271.660 - 0.021ms returns 0
T39A4 001:271.677 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:271.689 - 0.016ms returns 0
T39A4 001:271.702 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:271.714 - 0.016ms returns 0
T39A4 001:271.725 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:271.736 - 0.016ms returns 0
T39A4 001:271.749 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:271.761 - 0.017ms returns 0
T39A4 001:271.774 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:271.788 - 0.020ms returns 0
T39A4 001:271.805 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:271.819 - 0.019ms returns 0
T39A4 001:271.835 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:271.849 - 0.020ms returns 0
T39A4 001:271.865 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:271.878 - 0.019ms returns 0
T39A4 001:271.893 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:271.907 - 0.020ms returns 0
T39A4 001:271.922 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:271.936 - 0.019ms returns 0
T39A4 001:271.951 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:271.964 - 0.018ms returns 0
T39A4 001:271.979 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:271.996 - 0.022ms returns 0x00000025
T39A4 001:272.011 JLINK_Go()
T39A4 001:272.045   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:275.559 - 3.588ms
T39A4 001:275.624 JLINK_IsHalted()
T39A4 001:276.064 - 0.459ms returns FALSE
T39A4 001:276.104 JLINK_HasError()
T39A4 001:279.673 JLINK_IsHalted()
T39A4 001:280.177 - 0.523ms returns FALSE
T39A4 001:280.212 JLINK_HasError()
T39A4 001:282.715 JLINK_IsHalted()
T39A4 001:283.231 - 0.546ms returns FALSE
T39A4 001:283.278 JLINK_HasError()
T39A4 001:291.640 JLINK_IsHalted()
T39A4 001:292.230 - 0.614ms returns FALSE
T39A4 001:292.270 JLINK_HasError()
T39A4 001:293.650 JLINK_IsHalted()
T39A4 001:294.117 - 0.486ms returns FALSE
T39A4 001:294.151 JLINK_HasError()
T39A4 001:297.616 JLINK_IsHalted()
T39A4 001:298.090 - 0.490ms returns FALSE
T39A4 001:298.119 JLINK_HasError()
T39A4 001:299.607 JLINK_IsHalted()
T39A4 001:300.078 - 0.495ms returns FALSE
T39A4 001:300.132 JLINK_HasError()
T39A4 001:304.632 JLINK_IsHalted()
T39A4 001:305.115 - 0.501ms returns FALSE
T39A4 001:305.148 JLINK_HasError()
T39A4 001:306.596 JLINK_IsHalted()
T39A4 001:309.829   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:310.299 - 3.717ms returns TRUE
T39A4 001:310.328 JLINK_ReadReg(R15 (PC))
T39A4 001:310.346 - 0.024ms returns 0x20000000
T39A4 001:310.361 JLINK_ClrBPEx(BPHandle = 0x00000025)
T39A4 001:310.375 - 0.019ms returns 0x00
T39A4 001:310.389 JLINK_ReadReg(R0)
T39A4 001:310.401 - 0.017ms returns 0x00000000
T39A4 001:311.619 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:311.668   Data:  01 68 21 F4 00 61 01 60 22 20 84 F8 3D 00 40 20 ...
T39A4 001:311.731   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:324.068 - 12.491ms returns 0x400
T39A4 001:324.131 JLINK_HasError()
T39A4 001:324.149 JLINK_WriteReg(R0, 0x08001000)
T39A4 001:324.172 - 0.029ms returns 0
T39A4 001:324.187 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:324.209 - 0.027ms returns 0
T39A4 001:324.224 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:324.236 - 0.017ms returns 0
T39A4 001:324.250 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:324.261 - 0.016ms returns 0
T39A4 001:324.275 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:324.289 - 0.019ms returns 0
T39A4 001:324.303 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:324.316 - 0.018ms returns 0
T39A4 001:324.330 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:324.342 - 0.018ms returns 0
T39A4 001:324.356 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:324.368 - 0.017ms returns 0
T39A4 001:324.382 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:324.395 - 0.018ms returns 0
T39A4 001:324.409 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:324.421 - 0.017ms returns 0
T39A4 001:324.435 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:324.448 - 0.019ms returns 0
T39A4 001:324.463 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:324.475 - 0.016ms returns 0
T39A4 001:324.488 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:324.500 - 0.016ms returns 0
T39A4 001:324.512 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:324.529 - 0.023ms returns 0
T39A4 001:324.545 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:324.557 - 0.018ms returns 0
T39A4 001:324.572 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:324.584 - 0.017ms returns 0
T39A4 001:324.598 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:324.610 - 0.017ms returns 0
T39A4 001:324.624 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:324.635 - 0.017ms returns 0
T39A4 001:324.650 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:324.662 - 0.017ms returns 0
T39A4 001:324.676 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:324.687 - 0.017ms returns 0
T39A4 001:324.702 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:324.716 - 0.019ms returns 0x00000026
T39A4 001:324.730 JLINK_Go()
T39A4 001:324.759   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:328.200 - 3.499ms
T39A4 001:328.249 JLINK_IsHalted()
T39A4 001:328.656 - 0.423ms returns FALSE
T39A4 001:328.689 JLINK_HasError()
T39A4 001:335.505 JLINK_IsHalted()
T39A4 001:336.029 - 0.536ms returns FALSE
T39A4 001:336.054 JLINK_HasError()
T39A4 001:337.525 JLINK_IsHalted()
T39A4 001:338.035 - 0.527ms returns FALSE
T39A4 001:338.066 JLINK_HasError()
T39A4 001:339.495 JLINK_IsHalted()
T39A4 001:339.916 - 0.431ms returns FALSE
T39A4 001:339.938 JLINK_HasError()
T39A4 001:341.489 JLINK_IsHalted()
T39A4 001:341.896 - 0.418ms returns FALSE
T39A4 001:341.918 JLINK_HasError()
T39A4 001:343.487 JLINK_IsHalted()
T39A4 001:343.993 - 0.515ms returns FALSE
T39A4 001:344.015 JLINK_HasError()
T39A4 001:345.481 JLINK_IsHalted()
T39A4 001:345.902 - 0.430ms returns FALSE
T39A4 001:345.922 JLINK_HasError()
T39A4 001:349.474 JLINK_IsHalted()
T39A4 001:349.918 - 0.454ms returns FALSE
T39A4 001:349.941 JLINK_HasError()
T39A4 001:351.468 JLINK_IsHalted()
T39A4 001:351.883 - 0.425ms returns FALSE
T39A4 001:351.906 JLINK_HasError()
T39A4 001:362.940 JLINK_IsHalted()
T39A4 001:366.076   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:366.513 - 3.583ms returns TRUE
T39A4 001:366.537 JLINK_ReadReg(R15 (PC))
T39A4 001:366.553 - 0.021ms returns 0x20000000
T39A4 001:366.567 JLINK_ClrBPEx(BPHandle = 0x00000026)
T39A4 001:366.579 - 0.017ms returns 0x00
T39A4 001:366.593 JLINK_ReadReg(R0)
T39A4 001:366.604 - 0.016ms returns 0x00000000
T39A4 001:367.418 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:367.435   Data:  2D E9 F0 47 0F 46 05 00 61 D0 43 4E 30 68 00 F0 ...
T39A4 001:367.463   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:379.712 - 12.329ms returns 0x400
T39A4 001:379.765 JLINK_HasError()
T39A4 001:379.783 JLINK_WriteReg(R0, 0x08001400)
T39A4 001:379.804 - 0.026ms returns 0
T39A4 001:379.817 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:379.826 - 0.013ms returns 0
T39A4 001:379.838 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:379.868 - 0.034ms returns 0
T39A4 001:379.880 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:379.892 - 0.017ms returns 0
T39A4 001:379.955 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:379.971 - 0.021ms returns 0
T39A4 001:379.984 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:379.994 - 0.014ms returns 0
T39A4 001:380.005 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:380.014 - 0.012ms returns 0
T39A4 001:380.024 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:380.033 - 0.013ms returns 0
T39A4 001:380.044 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:380.054 - 0.014ms returns 0
T39A4 001:380.066 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:380.076 - 0.015ms returns 0
T39A4 001:380.088 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:380.099 - 0.015ms returns 0
T39A4 001:380.111 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:380.121 - 0.015ms returns 0
T39A4 001:380.135 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:380.146 - 0.015ms returns 0
T39A4 001:380.158 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:380.170 - 0.016ms returns 0
T39A4 001:380.182 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:380.193 - 0.015ms returns 0
T39A4 001:380.205 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:380.216 - 0.016ms returns 0
T39A4 001:380.228 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:380.238 - 0.014ms returns 0
T39A4 001:380.249 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:380.260 - 0.017ms returns 0
T39A4 001:380.273 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:380.284 - 0.015ms returns 0
T39A4 001:380.297 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:380.308 - 0.016ms returns 0
T39A4 001:380.322 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:380.335 - 0.018ms returns 0x00000027
T39A4 001:380.348 JLINK_Go()
T39A4 001:380.389   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:384.049 - 3.747ms
T39A4 001:384.117 JLINK_IsHalted()
T39A4 001:384.541 - 0.438ms returns FALSE
T39A4 001:384.568 JLINK_HasError()
T39A4 001:387.892 JLINK_IsHalted()
T39A4 001:388.377 - 0.503ms returns FALSE
T39A4 001:388.410 JLINK_HasError()
T39A4 001:389.891 JLINK_IsHalted()
T39A4 001:390.365 - 0.493ms returns FALSE
T39A4 001:390.397 JLINK_HasError()
T39A4 001:391.900 JLINK_IsHalted()
T39A4 001:392.367 - 0.483ms returns FALSE
T39A4 001:392.397 JLINK_HasError()
T39A4 001:415.810 JLINK_IsHalted()
T39A4 001:419.098   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:419.532 - 3.735ms returns TRUE
T39A4 001:419.559 JLINK_ReadReg(R15 (PC))
T39A4 001:419.578 - 0.024ms returns 0x20000000
T39A4 001:419.593 JLINK_ClrBPEx(BPHandle = 0x00000027)
T39A4 001:419.606 - 0.019ms returns 0x00
T39A4 001:419.622 JLINK_ReadReg(R0)
T39A4 001:419.635 - 0.019ms returns 0x00000000
T39A4 001:420.528 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:420.555   Data:  E8 69 20 F0 80 50 E8 61 E0 69 88 B3 69 68 C1 F3 ...
T39A4 001:420.584   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:432.809 - 12.303ms returns 0x400
T39A4 001:432.847 JLINK_HasError()
T39A4 001:432.862 JLINK_WriteReg(R0, 0x08001800)
T39A4 001:432.877 - 0.018ms returns 0
T39A4 001:432.891 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:432.903 - 0.018ms returns 0
T39A4 001:432.919 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:432.930 - 0.016ms returns 0
T39A4 001:432.945 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:432.955 - 0.015ms returns 0
T39A4 001:432.970 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:432.981 - 0.016ms returns 0
T39A4 001:432.996 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:433.007 - 0.015ms returns 0
T39A4 001:433.020 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:433.031 - 0.015ms returns 0
T39A4 001:433.045 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:433.055 - 0.014ms returns 0
T39A4 001:433.070 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:433.081 - 0.017ms returns 0
T39A4 001:433.096 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:433.108 - 0.017ms returns 0
T39A4 001:433.124 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:433.136 - 0.017ms returns 0
T39A4 001:433.151 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:433.163 - 0.017ms returns 0
T39A4 001:433.178 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:433.281 - 0.113ms returns 0
T39A4 001:433.302 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:433.315 - 0.018ms returns 0
T39A4 001:433.330 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:433.394 - 0.070ms returns 0
T39A4 001:433.410 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:433.421 - 0.014ms returns 0
T39A4 001:433.434 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:433.446 - 0.017ms returns 0
T39A4 001:433.460 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:433.472 - 0.017ms returns 0
T39A4 001:433.487 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:433.498 - 0.015ms returns 0
T39A4 001:433.512 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:433.524 - 0.016ms returns 0
T39A4 001:433.539 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:433.552 - 0.017ms returns 0x00000028
T39A4 001:433.566 JLINK_Go()
T39A4 001:433.595   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:437.030 - 3.484ms
T39A4 001:437.066 JLINK_IsHalted()
T39A4 001:437.458 - 0.405ms returns FALSE
T39A4 001:437.486 JLINK_HasError()
T39A4 001:440.745 JLINK_IsHalted()
T39A4 001:441.219 - 0.486ms returns FALSE
T39A4 001:441.241 JLINK_HasError()
T39A4 001:442.744 JLINK_IsHalted()
T39A4 001:443.204 - 0.473ms returns FALSE
T39A4 001:443.228 JLINK_HasError()
T39A4 001:444.736 JLINK_IsHalted()
T39A4 001:445.193 - 0.477ms returns FALSE
T39A4 001:445.223 JLINK_HasError()
T39A4 001:446.761 JLINK_IsHalted()
T39A4 001:447.216 - 0.467ms returns FALSE
T39A4 001:447.237 JLINK_HasError()
T39A4 001:448.722 JLINK_IsHalted()
T39A4 001:449.184 - 0.476ms returns FALSE
T39A4 001:449.216 JLINK_HasError()
T39A4 001:450.716 JLINK_IsHalted()
T39A4 001:451.141 - 0.438ms returns FALSE
T39A4 001:451.165 JLINK_HasError()
T39A4 001:452.728 JLINK_IsHalted()
T39A4 001:453.246 - 0.534ms returns FALSE
T39A4 001:453.274 JLINK_HasError()
T39A4 001:454.713 JLINK_IsHalted()
T39A4 001:455.200 - 0.507ms returns FALSE
T39A4 001:455.234 JLINK_HasError()
T39A4 001:456.699 JLINK_IsHalted()
T39A4 001:457.149 - 0.466ms returns FALSE
T39A4 001:457.180 JLINK_HasError()
T39A4 001:458.700 JLINK_IsHalted()
T39A4 001:459.228 - 0.545ms returns FALSE
T39A4 001:459.257 JLINK_HasError()
T39A4 001:460.697 JLINK_IsHalted()
T39A4 001:461.146 - 0.464ms returns FALSE
T39A4 001:461.171 JLINK_HasError()
T39A4 001:463.200 JLINK_IsHalted()
T39A4 001:463.668 - 0.481ms returns FALSE
T39A4 001:463.695 JLINK_HasError()
T39A4 001:469.176 JLINK_IsHalted()
T39A4 001:472.346   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:472.800 - 3.633ms returns TRUE
T39A4 001:472.819 JLINK_ReadReg(R15 (PC))
T39A4 001:472.830 - 0.014ms returns 0x20000000
T39A4 001:472.839 JLINK_ClrBPEx(BPHandle = 0x00000028)
T39A4 001:472.847 - 0.011ms returns 0x00
T39A4 001:472.857 JLINK_ReadReg(R0)
T39A4 001:472.864 - 0.011ms returns 0x00000000
T39A4 001:473.497 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:473.511   Data:  04 01 81 61 80 69 12 4D 00 F0 04 00 04 90 4F F4 ...
T39A4 001:473.532   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:485.737 - 12.264ms returns 0x400
T39A4 001:485.775 JLINK_HasError()
T39A4 001:485.785 JLINK_WriteReg(R0, 0x08001C00)
T39A4 001:485.798 - 0.016ms returns 0
T39A4 001:485.807 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:485.815 - 0.011ms returns 0
T39A4 001:485.823 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:485.830 - 0.010ms returns 0
T39A4 001:485.838 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:485.846 - 0.010ms returns 0
T39A4 001:485.854 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:485.861 - 0.010ms returns 0
T39A4 001:485.869 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:485.876 - 0.010ms returns 0
T39A4 001:485.884 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:485.891 - 0.010ms returns 0
T39A4 001:485.900 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:485.907 - 0.010ms returns 0
T39A4 001:485.915 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:485.922 - 0.010ms returns 0
T39A4 001:485.930 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:485.937 - 0.010ms returns 0
T39A4 001:485.945 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:485.952 - 0.010ms returns 0
T39A4 001:486.021 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:486.034 - 0.016ms returns 0
T39A4 001:486.042 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:486.049 - 0.010ms returns 0
T39A4 001:486.058 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:486.066 - 0.011ms returns 0
T39A4 001:486.074 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:486.081 - 0.010ms returns 0
T39A4 001:486.089 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:486.096 - 0.010ms returns 0
T39A4 001:486.104 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:486.113 - 0.013ms returns 0
T39A4 001:486.127 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:486.140 - 0.017ms returns 0
T39A4 001:486.154 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:486.165 - 0.016ms returns 0
T39A4 001:486.177 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:486.188 - 0.018ms returns 0
T39A4 001:486.202 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:486.212 - 0.013ms returns 0x00000029
T39A4 001:486.221 JLINK_Go()
T39A4 001:486.240   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:489.654 - 3.450ms
T39A4 001:489.684 JLINK_IsHalted()
T39A4 001:490.052 - 0.376ms returns FALSE
T39A4 001:490.070 JLINK_HasError()
T39A4 001:492.132 JLINK_IsHalted()
T39A4 001:492.552 - 0.431ms returns FALSE
T39A4 001:492.573 JLINK_HasError()
T39A4 001:494.120 JLINK_IsHalted()
T39A4 001:494.576 - 0.466ms returns FALSE
T39A4 001:494.596 JLINK_HasError()
T39A4 001:497.100 JLINK_IsHalted()
T39A4 001:497.522 - 0.430ms returns FALSE
T39A4 001:497.538 JLINK_HasError()
T39A4 001:499.086 JLINK_IsHalted()
T39A4 001:499.478 - 0.399ms returns FALSE
T39A4 001:499.494 JLINK_HasError()
T39A4 001:501.107 JLINK_IsHalted()
T39A4 001:501.540 - 0.443ms returns FALSE
T39A4 001:501.558 JLINK_HasError()
T39A4 001:503.080 JLINK_IsHalted()
T39A4 001:503.504 - 0.431ms returns FALSE
T39A4 001:503.519 JLINK_HasError()
T39A4 001:505.071 JLINK_IsHalted()
T39A4 001:505.462 - 0.400ms returns FALSE
T39A4 001:505.480 JLINK_HasError()
T39A4 001:507.065 JLINK_IsHalted()
T39A4 001:507.453 - 0.396ms returns FALSE
T39A4 001:507.469 JLINK_HasError()
T39A4 001:509.060 JLINK_IsHalted()
T39A4 001:509.467 - 0.419ms returns FALSE
T39A4 001:509.491 JLINK_HasError()
T39A4 001:511.061 JLINK_IsHalted()
T39A4 001:511.467 - 0.420ms returns FALSE
T39A4 001:511.491 JLINK_HasError()
T39A4 001:513.048 JLINK_IsHalted()
T39A4 001:513.451 - 0.411ms returns FALSE
T39A4 001:513.468 JLINK_HasError()
T39A4 001:515.049 JLINK_IsHalted()
T39A4 001:515.444 - 0.403ms returns FALSE
T39A4 001:515.467 JLINK_HasError()
T39A4 001:517.040 JLINK_IsHalted()
T39A4 001:517.442 - 0.412ms returns FALSE
T39A4 001:517.460 JLINK_HasError()
T39A4 001:519.035 JLINK_IsHalted()
T39A4 001:519.435 - 0.410ms returns FALSE
T39A4 001:519.453 JLINK_HasError()
T39A4 001:521.029 JLINK_IsHalted()
T39A4 001:524.164   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:524.577 - 3.557ms returns TRUE
T39A4 001:524.595 JLINK_ReadReg(R15 (PC))
T39A4 001:524.611 - 0.020ms returns 0x20000000
T39A4 001:524.623 JLINK_ClrBPEx(BPHandle = 0x00000029)
T39A4 001:524.631 - 0.011ms returns 0x00
T39A4 001:524.640 JLINK_ReadReg(R0)
T39A4 001:524.647 - 0.010ms returns 0x00000000
T39A4 001:525.213 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:525.225   Data:  09 69 42 6A 11 70 41 6A 49 1C 41 62 42 8D 52 1E ...
T39A4 001:525.243   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:537.414 - 12.222ms returns 0x400
T39A4 001:537.446 JLINK_HasError()
T39A4 001:537.458 JLINK_WriteReg(R0, 0x08002000)
T39A4 001:537.470 - 0.016ms returns 0
T39A4 001:537.480 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:537.488 - 0.012ms returns 0
T39A4 001:537.498 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:537.506 - 0.011ms returns 0
T39A4 001:537.515 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:537.523 - 0.011ms returns 0
T39A4 001:537.532 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:537.540 - 0.011ms returns 0
T39A4 001:537.549 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:537.557 - 0.011ms returns 0
T39A4 001:537.566 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:537.574 - 0.011ms returns 0
T39A4 001:537.583 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:537.654 - 0.075ms returns 0
T39A4 001:537.665 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:537.673 - 0.011ms returns 0
T39A4 001:537.682 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:537.690 - 0.011ms returns 0
T39A4 001:537.699 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:537.707 - 0.011ms returns 0
T39A4 001:537.716 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:537.724 - 0.011ms returns 0
T39A4 001:537.733 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:537.741 - 0.011ms returns 0
T39A4 001:537.750 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:537.759 - 0.013ms returns 0
T39A4 001:537.769 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:537.776 - 0.011ms returns 0
T39A4 001:537.785 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:537.794 - 0.011ms returns 0
T39A4 001:537.803 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:537.810 - 0.011ms returns 0
T39A4 001:537.820 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:537.827 - 0.011ms returns 0
T39A4 001:537.836 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:537.844 - 0.011ms returns 0
T39A4 001:537.853 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:537.861 - 0.011ms returns 0
T39A4 001:537.871 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:537.880 - 0.013ms returns 0x0000002A
T39A4 001:537.890 JLINK_Go()
T39A4 001:537.908   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:541.599 - 3.726ms
T39A4 001:541.627 JLINK_IsHalted()
T39A4 001:542.187 - 0.573ms returns FALSE
T39A4 001:542.222 JLINK_HasError()
T39A4 001:543.987 JLINK_IsHalted()
T39A4 001:544.484 - 0.509ms returns FALSE
T39A4 001:544.506 JLINK_HasError()
T39A4 001:546.065 JLINK_IsHalted()
T39A4 001:546.573 - 0.523ms returns FALSE
T39A4 001:546.598 JLINK_HasError()
T39A4 001:548.049 JLINK_IsHalted()
T39A4 001:548.554 - 0.515ms returns FALSE
T39A4 001:548.575 JLINK_HasError()
T39A4 001:550.026 JLINK_IsHalted()
T39A4 001:550.527 - 0.512ms returns FALSE
T39A4 001:550.548 JLINK_HasError()
T39A4 001:551.986 JLINK_IsHalted()
T39A4 001:552.509 - 0.533ms returns FALSE
T39A4 001:552.529 JLINK_HasError()
T39A4 001:554.020 JLINK_IsHalted()
T39A4 001:554.545 - 0.535ms returns FALSE
T39A4 001:554.565 JLINK_HasError()
T39A4 001:556.008 JLINK_IsHalted()
T39A4 001:556.525 - 0.527ms returns FALSE
T39A4 001:556.545 JLINK_HasError()
T39A4 001:558.022 JLINK_IsHalted()
T39A4 001:558.540 - 0.528ms returns FALSE
T39A4 001:558.560 JLINK_HasError()
T39A4 001:562.967 JLINK_IsHalted()
T39A4 001:563.542 - 0.585ms returns FALSE
T39A4 001:563.562 JLINK_HasError()
T39A4 001:565.044 JLINK_IsHalted()
T39A4 001:565.588 - 0.554ms returns FALSE
T39A4 001:565.608 JLINK_HasError()
T39A4 001:566.941 JLINK_IsHalted()
T39A4 001:567.429 - 0.498ms returns FALSE
T39A4 001:567.449 JLINK_HasError()
T39A4 001:568.997 JLINK_IsHalted()
T39A4 001:569.501 - 0.513ms returns FALSE
T39A4 001:569.520 JLINK_HasError()
T39A4 001:570.967 JLINK_IsHalted()
T39A4 001:571.475 - 0.519ms returns FALSE
T39A4 001:571.495 JLINK_HasError()
T39A4 001:573.027 JLINK_IsHalted()
T39A4 001:576.287   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:576.858 - 3.843ms returns TRUE
T39A4 001:576.881 JLINK_ReadReg(R15 (PC))
T39A4 001:576.894 - 0.016ms returns 0x20000000
T39A4 001:576.904 JLINK_ClrBPEx(BPHandle = 0x0000002A)
T39A4 001:576.913 - 0.012ms returns 0x00
T39A4 001:576.924 JLINK_ReadReg(R0)
T39A4 001:576.932 - 0.012ms returns 0x00000000
T39A4 001:577.572 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:577.587   Data:  02 2A 0F D0 10 2A 0D D0 0C 68 44 F4 00 64 0C 60 ...
T39A4 001:577.608   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:589.958 - 12.408ms returns 0x400
T39A4 001:589.994 JLINK_HasError()
T39A4 001:590.006 JLINK_WriteReg(R0, 0x08002400)
T39A4 001:590.020 - 0.018ms returns 0
T39A4 001:590.030 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:590.039 - 0.012ms returns 0
T39A4 001:590.049 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:590.057 - 0.012ms returns 0
T39A4 001:590.067 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:590.075 - 0.012ms returns 0
T39A4 001:590.084 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:590.097 - 0.019ms returns 0
T39A4 001:590.109 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:590.118 - 0.012ms returns 0
T39A4 001:590.127 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:590.136 - 0.012ms returns 0
T39A4 001:590.145 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:590.153 - 0.011ms returns 0
T39A4 001:590.163 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:590.171 - 0.012ms returns 0
T39A4 001:590.181 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:590.189 - 0.011ms returns 0
T39A4 001:590.198 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:590.206 - 0.012ms returns 0
T39A4 001:590.216 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:590.224 - 0.011ms returns 0
T39A4 001:590.239 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:590.248 - 0.012ms returns 0
T39A4 001:590.257 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:590.267 - 0.013ms returns 0
T39A4 001:590.276 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:590.284 - 0.012ms returns 0
T39A4 001:590.294 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:590.303 - 0.012ms returns 0
T39A4 001:590.312 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:590.320 - 0.012ms returns 0
T39A4 001:590.330 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:590.338 - 0.012ms returns 0
T39A4 001:590.348 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:590.356 - 0.012ms returns 0
T39A4 001:590.366 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:590.374 - 0.012ms returns 0
T39A4 001:590.384 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:590.393 - 0.013ms returns 0x0000002B
T39A4 001:590.403 JLINK_Go()
T39A4 001:590.422   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:594.137 - 3.752ms
T39A4 001:594.169 JLINK_IsHalted()
T39A4 001:594.669 - 0.509ms returns FALSE
T39A4 001:594.690 JLINK_HasError()
T39A4 001:597.876 JLINK_IsHalted()
T39A4 001:598.390 - 0.526ms returns FALSE
T39A4 001:598.413 JLINK_HasError()
T39A4 001:599.863 JLINK_IsHalted()
T39A4 001:600.393 - 0.539ms returns FALSE
T39A4 001:600.413 JLINK_HasError()
T39A4 001:601.887 JLINK_IsHalted()
T39A4 001:602.404 - 0.526ms returns FALSE
T39A4 001:602.423 JLINK_HasError()
T39A4 001:603.927 JLINK_IsHalted()
T39A4 001:604.453 - 0.535ms returns FALSE
T39A4 001:604.473 JLINK_HasError()
T39A4 001:605.872 JLINK_IsHalted()
T39A4 001:606.386 - 0.523ms returns FALSE
T39A4 001:606.406 JLINK_HasError()
T39A4 001:607.918 JLINK_IsHalted()
T39A4 001:608.467 - 0.559ms returns FALSE
T39A4 001:608.487 JLINK_HasError()
T39A4 001:609.828 JLINK_IsHalted()
T39A4 001:610.391 - 0.573ms returns FALSE
T39A4 001:610.411 JLINK_HasError()
T39A4 001:611.864 JLINK_IsHalted()
T39A4 001:612.395 - 0.541ms returns FALSE
T39A4 001:612.415 JLINK_HasError()
T39A4 001:613.814 JLINK_IsHalted()
T39A4 001:614.299 - 0.495ms returns FALSE
T39A4 001:614.319 JLINK_HasError()
T39A4 001:615.832 JLINK_IsHalted()
T39A4 001:616.336 - 0.514ms returns FALSE
T39A4 001:616.362 JLINK_HasError()
T39A4 001:617.813 JLINK_IsHalted()
T39A4 001:618.335 - 0.532ms returns FALSE
T39A4 001:618.356 JLINK_HasError()
T39A4 001:619.844 JLINK_IsHalted()
T39A4 001:620.391 - 0.557ms returns FALSE
T39A4 001:620.415 JLINK_HasError()
T39A4 001:621.818 JLINK_IsHalted()
T39A4 001:622.280 - 0.473ms returns FALSE
T39A4 001:622.301 JLINK_HasError()
T39A4 001:623.830 JLINK_IsHalted()
T39A4 001:624.308 - 0.488ms returns FALSE
T39A4 001:624.328 JLINK_HasError()
T39A4 001:625.783 JLINK_IsHalted()
T39A4 001:629.058   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:629.606 - 3.834ms returns TRUE
T39A4 001:629.628 JLINK_ReadReg(R15 (PC))
T39A4 001:629.641 - 0.016ms returns 0x20000000
T39A4 001:629.651 JLINK_ClrBPEx(BPHandle = 0x0000002B)
T39A4 001:629.661 - 0.013ms returns 0x00
T39A4 001:629.672 JLINK_ReadReg(R0)
T39A4 001:629.680 - 0.012ms returns 0x00000000
T39A4 001:630.311 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:630.326   Data:  41 69 C9 43 49 07 08 D4 00 69 61 6A 08 70 60 6A ...
T39A4 001:630.346   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:642.641 - 12.349ms returns 0x400
T39A4 001:642.673 JLINK_HasError()
T39A4 001:642.685 JLINK_WriteReg(R0, 0x08002800)
T39A4 001:642.706 - 0.028ms returns 0
T39A4 001:642.720 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:642.730 - 0.013ms returns 0
T39A4 001:642.740 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:642.749 - 0.012ms returns 0
T39A4 001:642.759 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:642.767 - 0.012ms returns 0
T39A4 001:642.777 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:642.786 - 0.012ms returns 0
T39A4 001:642.796 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:642.804 - 0.012ms returns 0
T39A4 001:642.814 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:642.823 - 0.012ms returns 0
T39A4 001:642.832 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:642.841 - 0.012ms returns 0
T39A4 001:642.851 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:642.859 - 0.012ms returns 0
T39A4 001:642.869 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:642.878 - 0.012ms returns 0
T39A4 001:642.888 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:642.896 - 0.012ms returns 0
T39A4 001:642.906 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:642.915 - 0.012ms returns 0
T39A4 001:642.925 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:642.933 - 0.013ms returns 0
T39A4 001:642.944 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:642.954 - 0.014ms returns 0
T39A4 001:642.964 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:642.977 - 0.016ms returns 0
T39A4 001:642.987 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:642.995 - 0.012ms returns 0
T39A4 001:643.005 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:643.014 - 0.012ms returns 0
T39A4 001:643.023 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:643.032 - 0.012ms returns 0
T39A4 001:643.042 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:643.050 - 0.012ms returns 0
T39A4 001:643.060 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:643.068 - 0.012ms returns 0
T39A4 001:643.078 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:643.088 - 0.013ms returns 0x0000002C
T39A4 001:643.098 JLINK_Go()
T39A4 001:643.118   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:646.805 - 3.725ms
T39A4 001:646.838 JLINK_IsHalted()
T39A4 001:647.387 - 0.560ms returns FALSE
T39A4 001:647.410 JLINK_HasError()
T39A4 001:648.753 JLINK_IsHalted()
T39A4 001:649.260 - 0.518ms returns FALSE
T39A4 001:649.281 JLINK_HasError()
T39A4 001:650.717 JLINK_IsHalted()
T39A4 001:651.211 - 0.505ms returns FALSE
T39A4 001:651.232 JLINK_HasError()
T39A4 001:652.721 JLINK_IsHalted()
T39A4 001:653.227 - 0.516ms returns FALSE
T39A4 001:653.247 JLINK_HasError()
T39A4 001:654.739 JLINK_IsHalted()
T39A4 001:655.262 - 0.534ms returns FALSE
T39A4 001:655.284 JLINK_HasError()
T39A4 001:656.724 JLINK_IsHalted()
T39A4 001:657.205 - 0.492ms returns FALSE
T39A4 001:657.226 JLINK_HasError()
T39A4 001:658.694 JLINK_IsHalted()
T39A4 001:659.207 - 0.526ms returns FALSE
T39A4 001:659.231 JLINK_HasError()
T39A4 001:660.692 JLINK_IsHalted()
T39A4 001:661.207 - 0.525ms returns FALSE
T39A4 001:661.228 JLINK_HasError()
T39A4 001:664.704 JLINK_IsHalted()
T39A4 001:665.180 - 0.486ms returns FALSE
T39A4 001:665.200 JLINK_HasError()
T39A4 001:666.745 JLINK_IsHalted()
T39A4 001:667.262 - 0.527ms returns FALSE
T39A4 001:667.283 JLINK_HasError()
T39A4 001:668.783 JLINK_IsHalted()
T39A4 001:669.330 - 0.557ms returns FALSE
T39A4 001:669.351 JLINK_HasError()
T39A4 001:672.666 JLINK_IsHalted()
T39A4 001:673.183 - 0.527ms returns FALSE
T39A4 001:673.207 JLINK_HasError()
T39A4 001:674.651 JLINK_IsHalted()
T39A4 001:675.155 - 0.514ms returns FALSE
T39A4 001:675.175 JLINK_HasError()
T39A4 001:676.687 JLINK_IsHalted()
T39A4 001:677.179 - 0.502ms returns FALSE
T39A4 001:677.199 JLINK_HasError()
T39A4 001:678.640 JLINK_IsHalted()
T39A4 001:681.942   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:682.435 - 3.805ms returns TRUE
T39A4 001:682.457 JLINK_ReadReg(R15 (PC))
T39A4 001:682.471 - 0.017ms returns 0x20000000
T39A4 001:682.482 JLINK_ClrBPEx(BPHandle = 0x0000002C)
T39A4 001:682.491 - 0.013ms returns 0x00
T39A4 001:682.502 JLINK_ReadReg(R0)
T39A4 001:682.510 - 0.012ms returns 0x00000000
T39A4 001:683.187 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:683.203   Data:  10 B5 1F 48 1D 49 00 24 01 60 08 21 C0 E9 01 14 ...
T39A4 001:683.232   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:695.528 - 12.358ms returns 0x400
T39A4 001:695.562 JLINK_HasError()
T39A4 001:695.574 JLINK_WriteReg(R0, 0x08002C00)
T39A4 001:695.589 - 0.018ms returns 0
T39A4 001:695.599 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:695.608 - 0.012ms returns 0
T39A4 001:695.618 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:695.626 - 0.012ms returns 0
T39A4 001:695.636 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:695.644 - 0.012ms returns 0
T39A4 001:695.654 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:695.662 - 0.012ms returns 0
T39A4 001:695.672 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:695.680 - 0.012ms returns 0
T39A4 001:695.690 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:695.698 - 0.012ms returns 0
T39A4 001:695.708 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:695.718 - 0.015ms returns 0
T39A4 001:695.729 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:695.738 - 0.012ms returns 0
T39A4 001:695.747 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:695.756 - 0.012ms returns 0
T39A4 001:695.765 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:695.774 - 0.012ms returns 0
T39A4 001:695.783 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:695.791 - 0.012ms returns 0
T39A4 001:695.801 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:695.809 - 0.012ms returns 0
T39A4 001:695.819 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:695.828 - 0.012ms returns 0
T39A4 001:695.838 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:695.846 - 0.012ms returns 0
T39A4 001:695.856 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:695.864 - 0.012ms returns 0
T39A4 001:695.874 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:695.882 - 0.012ms returns 0
T39A4 001:695.892 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:695.900 - 0.012ms returns 0
T39A4 001:695.910 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:695.918 - 0.012ms returns 0
T39A4 001:695.928 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:695.936 - 0.012ms returns 0
T39A4 001:695.947 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:695.957 - 0.014ms returns 0x0000002D
T39A4 001:695.967 JLINK_Go()
T39A4 001:695.985   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:699.723 - 3.773ms
T39A4 001:699.755 JLINK_IsHalted()
T39A4 001:700.256 - 0.512ms returns FALSE
T39A4 001:700.279 JLINK_HasError()
T39A4 001:706.582 JLINK_IsHalted()
T39A4 001:707.127 - 0.556ms returns FALSE
T39A4 001:707.148 JLINK_HasError()
T39A4 001:708.564 JLINK_IsHalted()
T39A4 001:709.097 - 0.545ms returns FALSE
T39A4 001:709.120 JLINK_HasError()
T39A4 001:710.603 JLINK_IsHalted()
T39A4 001:711.128 - 0.536ms returns FALSE
T39A4 001:711.149 JLINK_HasError()
T39A4 001:712.552 JLINK_IsHalted()
T39A4 001:713.096 - 0.554ms returns FALSE
T39A4 001:713.116 JLINK_HasError()
T39A4 001:714.548 JLINK_IsHalted()
T39A4 001:715.090 - 0.554ms returns FALSE
T39A4 001:715.112 JLINK_HasError()
T39A4 001:716.549 JLINK_IsHalted()
T39A4 001:717.136 - 0.599ms returns FALSE
T39A4 001:717.158 JLINK_HasError()
T39A4 001:721.508 JLINK_IsHalted()
T39A4 001:722.102 - 0.606ms returns FALSE
T39A4 001:722.125 JLINK_HasError()
T39A4 001:725.536 JLINK_IsHalted()
T39A4 001:726.101 - 0.577ms returns FALSE
T39A4 001:726.128 JLINK_HasError()
T39A4 001:727.537 JLINK_IsHalted()
T39A4 001:728.010 - 0.483ms returns FALSE
T39A4 001:728.030 JLINK_HasError()
T39A4 001:729.548 JLINK_IsHalted()
T39A4 001:730.100 - 0.562ms returns FALSE
T39A4 001:730.121 JLINK_HasError()
T39A4 001:731.505 JLINK_IsHalted()
T39A4 001:734.767   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:735.292 - 3.798ms returns TRUE
T39A4 001:735.313 JLINK_ReadReg(R15 (PC))
T39A4 001:735.325 - 0.016ms returns 0x20000000
T39A4 001:735.336 JLINK_ClrBPEx(BPHandle = 0x0000002D)
T39A4 001:735.345 - 0.012ms returns 0x00
T39A4 001:735.355 JLINK_ReadReg(R0)
T39A4 001:735.364 - 0.012ms returns 0x00000000
T39A4 001:736.014 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:736.028   Data:  90 F8 42 10 22 29 01 D0 02 20 08 BD 82 68 4F F4 ...
T39A4 001:736.048   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:748.319 - 12.326ms returns 0x400
T39A4 001:748.356 JLINK_HasError()
T39A4 001:748.369 JLINK_WriteReg(R0, 0x08003000)
T39A4 001:748.383 - 0.018ms returns 0
T39A4 001:748.393 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:748.402 - 0.012ms returns 0
T39A4 001:748.413 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:748.422 - 0.012ms returns 0
T39A4 001:748.431 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:748.440 - 0.012ms returns 0
T39A4 001:748.449 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:748.458 - 0.011ms returns 0
T39A4 001:748.467 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:748.475 - 0.012ms returns 0
T39A4 001:748.485 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:748.493 - 0.012ms returns 0
T39A4 001:748.503 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:748.511 - 0.012ms returns 0
T39A4 001:748.521 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:748.529 - 0.012ms returns 0
T39A4 001:748.539 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:748.547 - 0.012ms returns 0
T39A4 001:748.557 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:748.565 - 0.012ms returns 0
T39A4 001:748.575 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:748.583 - 0.012ms returns 0
T39A4 001:748.593 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:748.601 - 0.012ms returns 0
T39A4 001:748.611 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:748.620 - 0.012ms returns 0
T39A4 001:748.629 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:748.638 - 0.012ms returns 0
T39A4 001:748.647 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:748.656 - 0.012ms returns 0
T39A4 001:748.666 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:748.674 - 0.012ms returns 0
T39A4 001:748.684 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:748.692 - 0.012ms returns 0
T39A4 001:748.702 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:748.710 - 0.012ms returns 0
T39A4 001:748.720 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:748.728 - 0.012ms returns 0
T39A4 001:748.738 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:748.748 - 0.013ms returns 0x0000002E
T39A4 001:748.758 JLINK_Go()
T39A4 001:748.777   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:752.472 - 3.730ms
T39A4 001:752.503 JLINK_IsHalted()
T39A4 001:753.005 - 0.512ms returns FALSE
T39A4 001:753.028 JLINK_HasError()
T39A4 001:756.457 JLINK_IsHalted()
T39A4 001:756.981 - 0.536ms returns FALSE
T39A4 001:757.003 JLINK_HasError()
T39A4 001:758.430 JLINK_IsHalted()
T39A4 001:758.962 - 0.544ms returns FALSE
T39A4 001:758.985 JLINK_HasError()
T39A4 001:760.426 JLINK_IsHalted()
T39A4 001:760.963 - 0.547ms returns FALSE
T39A4 001:760.983 JLINK_HasError()
T39A4 001:762.471 JLINK_IsHalted()
T39A4 001:762.981 - 0.520ms returns FALSE
T39A4 001:763.001 JLINK_HasError()
T39A4 001:764.432 JLINK_IsHalted()
T39A4 001:764.947 - 0.528ms returns FALSE
T39A4 001:764.971 JLINK_HasError()
T39A4 001:769.454 JLINK_IsHalted()
T39A4 001:770.029 - 0.586ms returns FALSE
T39A4 001:770.050 JLINK_HasError()
T39A4 001:771.399 JLINK_IsHalted()
T39A4 001:771.874 - 0.485ms returns FALSE
T39A4 001:771.894 JLINK_HasError()
T39A4 001:775.428 JLINK_IsHalted()
T39A4 001:775.954 - 0.540ms returns FALSE
T39A4 001:775.980 JLINK_HasError()
T39A4 001:777.385 JLINK_IsHalted()
T39A4 001:777.893 - 0.519ms returns FALSE
T39A4 001:777.914 JLINK_HasError()
T39A4 001:779.385 JLINK_IsHalted()
T39A4 001:779.862 - 0.488ms returns FALSE
T39A4 001:779.884 JLINK_HasError()
T39A4 001:781.337 JLINK_IsHalted()
T39A4 001:781.840 - 0.514ms returns FALSE
T39A4 001:781.861 JLINK_HasError()
T39A4 001:783.388 JLINK_IsHalted()
T39A4 001:786.710   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:787.236 - 3.858ms returns TRUE
T39A4 001:787.257 JLINK_ReadReg(R15 (PC))
T39A4 001:787.270 - 0.017ms returns 0x20000000
T39A4 001:787.281 JLINK_ClrBPEx(BPHandle = 0x0000002E)
T39A4 001:787.290 - 0.013ms returns 0x00
T39A4 001:787.301 JLINK_ReadReg(R0)
T39A4 001:787.312 - 0.015ms returns 0x00000000
T39A4 001:787.961 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:787.976   Data:  57 F8 04 9B 6D 1C 28 78 6C 28 0F D0 06 DC 4C 28 ...
T39A4 001:787.996   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:800.318 - 12.377ms returns 0x400
T39A4 001:800.352 JLINK_HasError()
T39A4 001:800.365 JLINK_WriteReg(R0, 0x08003400)
T39A4 001:800.380 - 0.019ms returns 0
T39A4 001:800.390 JLINK_WriteReg(R1, 0x00000400)
T39A4 001:800.399 - 0.012ms returns 0
T39A4 001:800.409 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:800.418 - 0.012ms returns 0
T39A4 001:800.428 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:800.437 - 0.012ms returns 0
T39A4 001:800.447 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:800.455 - 0.012ms returns 0
T39A4 001:800.465 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:800.473 - 0.012ms returns 0
T39A4 001:800.483 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:800.492 - 0.012ms returns 0
T39A4 001:800.502 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:800.511 - 0.012ms returns 0
T39A4 001:800.520 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:800.529 - 0.012ms returns 0
T39A4 001:800.539 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:800.547 - 0.012ms returns 0
T39A4 001:800.557 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:800.566 - 0.012ms returns 0
T39A4 001:800.576 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:800.584 - 0.012ms returns 0
T39A4 001:800.594 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:800.603 - 0.012ms returns 0
T39A4 001:800.613 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:800.622 - 0.013ms returns 0
T39A4 001:800.632 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:800.640 - 0.012ms returns 0
T39A4 001:800.650 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:800.659 - 0.012ms returns 0
T39A4 001:800.669 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:800.677 - 0.012ms returns 0
T39A4 001:800.687 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:800.696 - 0.012ms returns 0
T39A4 001:800.706 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:800.714 - 0.012ms returns 0
T39A4 001:800.724 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:800.733 - 0.012ms returns 0
T39A4 001:800.743 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:800.753 - 0.014ms returns 0x0000002F
T39A4 001:800.763 JLINK_Go()
T39A4 001:800.784   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:804.551 - 3.804ms
T39A4 001:804.583 JLINK_IsHalted()
T39A4 001:805.119 - 0.546ms returns FALSE
T39A4 001:805.142 JLINK_HasError()
T39A4 001:814.321 JLINK_IsHalted()
T39A4 001:814.856 - 0.545ms returns FALSE
T39A4 001:814.877 JLINK_HasError()
T39A4 001:816.276 JLINK_IsHalted()
T39A4 001:816.755 - 0.489ms returns FALSE
T39A4 001:816.776 JLINK_HasError()
T39A4 001:818.270 JLINK_IsHalted()
T39A4 001:818.754 - 0.495ms returns FALSE
T39A4 001:818.775 JLINK_HasError()
T39A4 001:820.311 JLINK_IsHalted()
T39A4 001:820.810 - 0.510ms returns FALSE
T39A4 001:820.833 JLINK_HasError()
T39A4 001:822.260 JLINK_IsHalted()
T39A4 001:822.782 - 0.533ms returns FALSE
T39A4 001:822.809 JLINK_HasError()
T39A4 001:824.277 JLINK_IsHalted()
T39A4 001:824.795 - 0.527ms returns FALSE
T39A4 001:824.815 JLINK_HasError()
T39A4 001:826.273 JLINK_IsHalted()
T39A4 001:826.840 - 0.577ms returns FALSE
T39A4 001:826.860 JLINK_HasError()
T39A4 001:829.266 JLINK_IsHalted()
T39A4 001:829.753 - 0.497ms returns FALSE
T39A4 001:829.774 JLINK_HasError()
T39A4 001:831.284 JLINK_IsHalted()
T39A4 001:831.793 - 0.520ms returns FALSE
T39A4 001:831.814 JLINK_HasError()
T39A4 001:833.234 JLINK_IsHalted()
T39A4 001:833.705 - 0.482ms returns FALSE
T39A4 001:833.726 JLINK_HasError()
T39A4 001:835.227 JLINK_IsHalted()
T39A4 001:838.491   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:839.028 - 3.813ms returns TRUE
T39A4 001:839.052 JLINK_ReadReg(R15 (PC))
T39A4 001:839.065 - 0.017ms returns 0x20000000
T39A4 001:839.077 JLINK_ClrBPEx(BPHandle = 0x0000002F)
T39A4 001:839.086 - 0.013ms returns 0x00
T39A4 001:839.097 JLINK_ReadReg(R0)
T39A4 001:839.106 - 0.012ms returns 0x00000000
T39A4 001:840.034 JLINK_WriteMem(0x20000164, 0x400 Bytes, ...)
T39A4 001:840.049   Data:  01 60 04 75 08 26 06 61 18 30 C4 60 84 60 A9 68 ...
T39A4 001:840.070   CPU_WriteMem(1024 bytes @ 0x20000164)
T39A4 001:852.380 - 12.366ms returns 0x400
T39A4 001:852.413 JLINK_HasError()
T39A4 001:852.429 JLINK_WriteReg(R0, 0x08003800)
T39A4 001:852.447 - 0.022ms returns 0
T39A4 001:852.458 JLINK_WriteReg(R1, 0x00000198)
T39A4 001:852.467 - 0.013ms returns 0
T39A4 001:852.477 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:852.526 - 0.054ms returns 0
T39A4 001:852.537 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:852.546 - 0.012ms returns 0
T39A4 001:852.556 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:852.564 - 0.012ms returns 0
T39A4 001:852.575 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:852.583 - 0.012ms returns 0
T39A4 001:852.593 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:852.602 - 0.015ms returns 0
T39A4 001:852.615 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:852.624 - 0.012ms returns 0
T39A4 001:852.634 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:852.642 - 0.012ms returns 0
T39A4 001:852.652 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:852.660 - 0.012ms returns 0
T39A4 001:852.670 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:852.685 - 0.019ms returns 0
T39A4 001:852.695 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:852.704 - 0.012ms returns 0
T39A4 001:852.713 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:852.722 - 0.012ms returns 0
T39A4 001:852.731 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:852.740 - 0.013ms returns 0
T39A4 001:852.750 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:852.758 - 0.012ms returns 0
T39A4 001:852.768 JLINK_WriteReg(R15 (PC), 0x200000F4)
T39A4 001:852.777 - 0.012ms returns 0
T39A4 001:852.786 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:852.795 - 0.012ms returns 0
T39A4 001:852.805 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:852.813 - 0.012ms returns 0
T39A4 001:852.822 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:852.831 - 0.012ms returns 0
T39A4 001:852.840 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:852.849 - 0.012ms returns 0
T39A4 001:852.859 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:852.869 - 0.014ms returns 0x00000030
T39A4 001:852.879 JLINK_Go()
T39A4 001:852.898   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:856.608 - 3.744ms
T39A4 001:856.637 JLINK_IsHalted()
T39A4 001:857.130 - 0.504ms returns FALSE
T39A4 001:857.158 JLINK_HasError()
T39A4 001:859.201 JLINK_IsHalted()
T39A4 001:859.729 - 0.538ms returns FALSE
T39A4 001:859.751 JLINK_HasError()
T39A4 001:861.209 JLINK_IsHalted()
T39A4 001:861.738 - 0.540ms returns FALSE
T39A4 001:861.759 JLINK_HasError()
T39A4 001:863.187 JLINK_IsHalted()
T39A4 001:863.659 - 0.482ms returns FALSE
T39A4 001:863.679 JLINK_HasError()
T39A4 001:865.147 JLINK_IsHalted()
T39A4 001:865.622 - 0.486ms returns FALSE
T39A4 001:865.643 JLINK_HasError()
T39A4 001:867.208 JLINK_IsHalted()
T39A4 001:867.713 - 0.514ms returns FALSE
T39A4 001:867.732 JLINK_HasError()
T39A4 001:869.393 JLINK_IsHalted()
T39A4 001:872.990   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:873.593 - 4.220ms returns TRUE
T39A4 001:873.625 JLINK_ReadReg(R15 (PC))
T39A4 001:873.638 - 0.017ms returns 0x20000000
T39A4 001:873.649 JLINK_ClrBPEx(BPHandle = 0x00000030)
T39A4 001:873.658 - 0.013ms returns 0x00
T39A4 001:873.669 JLINK_ReadReg(R0)
T39A4 001:873.678 - 0.012ms returns 0x00000000
T39A4 001:873.689 JLINK_HasError()
T39A4 001:873.700 JLINK_WriteReg(R0, 0x00000002)
T39A4 001:873.709 - 0.013ms returns 0
T39A4 001:873.719 JLINK_WriteReg(R1, 0x00000198)
T39A4 001:873.728 - 0.012ms returns 0
T39A4 001:873.738 JLINK_WriteReg(R2, 0x20000164)
T39A4 001:873.746 - 0.012ms returns 0
T39A4 001:873.756 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:873.765 - 0.012ms returns 0
T39A4 001:873.775 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:873.783 - 0.012ms returns 0
T39A4 001:873.793 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:873.802 - 0.012ms returns 0
T39A4 001:873.812 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:873.820 - 0.012ms returns 0
T39A4 001:873.830 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:873.838 - 0.012ms returns 0
T39A4 001:873.848 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:873.857 - 0.012ms returns 0
T39A4 001:873.867 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:873.875 - 0.012ms returns 0
T39A4 001:873.885 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:873.898 - 0.020ms returns 0
T39A4 001:873.912 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:873.920 - 0.012ms returns 0
T39A4 001:873.930 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:873.939 - 0.012ms returns 0
T39A4 001:873.949 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:873.957 - 0.012ms returns 0
T39A4 001:873.967 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:873.976 - 0.012ms returns 0
T39A4 001:873.986 JLINK_WriteReg(R15 (PC), 0x2000006A)
T39A4 001:873.994 - 0.012ms returns 0
T39A4 001:874.004 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:874.012 - 0.012ms returns 0
T39A4 001:874.022 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:874.031 - 0.012ms returns 0
T39A4 001:874.041 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:874.049 - 0.012ms returns 0
T39A4 001:874.059 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:874.068 - 0.012ms returns 0
T39A4 001:874.078 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:874.087 - 0.013ms returns 0x00000031
T39A4 001:874.098 JLINK_Go()
T39A4 001:874.117   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:878.008 - 3.920ms
T39A4 001:878.028 JLINK_IsHalted()
T39A4 001:881.340   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:881.969 - 3.951ms returns TRUE
T39A4 001:881.988 JLINK_ReadReg(R15 (PC))
T39A4 001:881.999 - 0.015ms returns 0x20000000
T39A4 001:882.010 JLINK_ClrBPEx(BPHandle = 0x00000031)
T39A4 001:882.019 - 0.012ms returns 0x00
T39A4 001:882.029 JLINK_ReadReg(R0)
T39A4 001:882.037 - 0.012ms returns 0x00000000
T39A4 001:944.583 JLINK_WriteMem(0x20000000, 0x164 Bytes, ...)
T39A4 001:944.602   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T39A4 001:944.630   CPU_WriteMem(356 bytes @ 0x20000000)
T39A4 001:949.450 - 4.891ms returns 0x164
T39A4 001:949.533 JLINK_HasError()
T39A4 001:949.551 JLINK_WriteReg(R0, 0x08000000)
T39A4 001:949.567 - 0.021ms returns 0
T39A4 001:949.581 JLINK_WriteReg(R1, 0x007A1200)
T39A4 001:949.592 - 0.016ms returns 0
T39A4 001:949.605 JLINK_WriteReg(R2, 0x00000003)
T39A4 001:949.616 - 0.016ms returns 0
T39A4 001:949.629 JLINK_WriteReg(R3, 0x00000000)
T39A4 001:949.640 - 0.016ms returns 0
T39A4 001:949.653 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:949.664 - 0.015ms returns 0
T39A4 001:949.677 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:949.687 - 0.015ms returns 0
T39A4 001:949.700 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:949.710 - 0.015ms returns 0
T39A4 001:949.722 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:949.732 - 0.015ms returns 0
T39A4 001:949.745 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:949.755 - 0.015ms returns 0
T39A4 001:949.767 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:949.777 - 0.015ms returns 0
T39A4 001:949.790 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:949.800 - 0.015ms returns 0
T39A4 001:949.812 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:949.823 - 0.015ms returns 0
T39A4 001:949.835 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:949.845 - 0.015ms returns 0
T39A4 001:949.857 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:949.868 - 0.016ms returns 0
T39A4 001:949.881 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:949.892 - 0.015ms returns 0
T39A4 001:949.904 JLINK_WriteReg(R15 (PC), 0x20000038)
T39A4 001:949.915 - 0.015ms returns 0
T39A4 001:949.928 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:949.941 - 0.018ms returns 0
T39A4 001:949.954 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:949.964 - 0.015ms returns 0
T39A4 001:949.976 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:949.987 - 0.015ms returns 0
T39A4 001:949.999 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:950.010 - 0.016ms returns 0
T39A4 001:950.024 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:950.042   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:950.771 - 0.772ms returns 0x00000032
T39A4 001:950.811 JLINK_Go()
T39A4 001:950.830   CPU_WriteMem(2 bytes @ 0x20000000)
T39A4 001:951.619   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:956.030 - 5.245ms
T39A4 001:956.072 JLINK_IsHalted()
T39A4 001:959.624   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 001:960.244 - 4.186ms returns TRUE
T39A4 001:960.273 JLINK_ReadReg(R15 (PC))
T39A4 001:960.325 - 0.058ms returns 0x20000000
T39A4 001:960.341 JLINK_ClrBPEx(BPHandle = 0x00000032)
T39A4 001:960.353 - 0.017ms returns 0x00
T39A4 001:960.367 JLINK_ReadReg(R0)
T39A4 001:960.378 - 0.016ms returns 0x00000000
T39A4 001:960.394 JLINK_HasError()
T39A4 001:960.409 JLINK_WriteReg(R0, 0xFFFFFFFF)
T39A4 001:960.422 - 0.019ms returns 0
T39A4 001:960.436 JLINK_WriteReg(R1, 0x08000000)
T39A4 001:960.448 - 0.017ms returns 0
T39A4 001:960.461 JLINK_WriteReg(R2, 0x00003998)
T39A4 001:960.472 - 0.016ms returns 0
T39A4 001:960.486 JLINK_WriteReg(R3, 0x04C11DB7)
T39A4 001:960.497 - 0.016ms returns 0
T39A4 001:960.511 JLINK_WriteReg(R4, 0x00000000)
T39A4 001:960.523 - 0.017ms returns 0
T39A4 001:960.536 JLINK_WriteReg(R5, 0x00000000)
T39A4 001:960.547 - 0.016ms returns 0
T39A4 001:960.561 JLINK_WriteReg(R6, 0x00000000)
T39A4 001:960.572 - 0.016ms returns 0
T39A4 001:960.586 JLINK_WriteReg(R7, 0x00000000)
T39A4 001:960.597 - 0.016ms returns 0
T39A4 001:960.611 JLINK_WriteReg(R8, 0x00000000)
T39A4 001:960.624 - 0.018ms returns 0
T39A4 001:960.637 JLINK_WriteReg(R9, 0x20000160)
T39A4 001:960.648 - 0.016ms returns 0
T39A4 001:960.662 JLINK_WriteReg(R10, 0x00000000)
T39A4 001:960.673 - 0.016ms returns 0
T39A4 001:960.686 JLINK_WriteReg(R11, 0x00000000)
T39A4 001:960.697 - 0.016ms returns 0
T39A4 001:960.710 JLINK_WriteReg(R12, 0x00000000)
T39A4 001:960.721 - 0.016ms returns 0
T39A4 001:960.734 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 001:960.746 - 0.017ms returns 0
T39A4 001:960.760 JLINK_WriteReg(R14, 0x20000001)
T39A4 001:960.771 - 0.016ms returns 0
T39A4 001:960.784 JLINK_WriteReg(R15 (PC), 0x20000002)
T39A4 001:960.796 - 0.016ms returns 0
T39A4 001:960.809 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 001:960.820 - 0.016ms returns 0
T39A4 001:960.833 JLINK_WriteReg(MSP, 0x20001000)
T39A4 001:960.850 - 0.024ms returns 0
T39A4 001:960.866 JLINK_WriteReg(PSP, 0x20001000)
T39A4 001:960.878 - 0.016ms returns 0
T39A4 001:960.891 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 001:960.902 - 0.016ms returns 0
T39A4 001:960.915 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 001:960.929 - 0.018ms returns 0x00000033
T39A4 001:961.040 JLINK_Go()
T39A4 001:961.077   CPU_ReadMem(4 bytes @ 0x********)
T39A4 001:964.749 - 3.727ms
T39A4 001:964.781 JLINK_IsHalted()
T39A4 001:965.248 - 0.481ms returns FALSE
T39A4 001:965.276 JLINK_HasError()
T39A4 001:973.176 JLINK_IsHalted()
T39A4 001:973.976 - 0.824ms returns FALSE
T39A4 001:974.013 JLINK_HasError()
T39A4 001:975.187 JLINK_IsHalted()
T39A4 001:975.750 - 0.580ms returns FALSE
T39A4 001:975.780 JLINK_HasError()
T39A4 001:977.033 JLINK_IsHalted()
T39A4 001:977.776 - 0.756ms returns FALSE
T39A4 001:977.803 JLINK_HasError()
T39A4 001:979.229 JLINK_IsHalted()
T39A4 001:980.004 - 0.807ms returns FALSE
T39A4 001:980.052 JLINK_HasError()
T39A4 001:982.240 JLINK_IsHalted()
T39A4 001:983.019 - 0.804ms returns FALSE
T39A4 001:983.057 JLINK_HasError()
T39A4 001:985.126 JLINK_IsHalted()
T39A4 001:985.871 - 0.772ms returns FALSE
T39A4 001:985.913 JLINK_HasError()
T39A4 001:987.137 JLINK_IsHalted()
T39A4 001:987.904 - 0.792ms returns FALSE
T39A4 001:987.949 JLINK_HasError()
T39A4 001:989.122 JLINK_IsHalted()
T39A4 001:989.873 - 0.777ms returns FALSE
T39A4 001:989.916 JLINK_HasError()
T39A4 001:991.121 JLINK_IsHalted()
T39A4 001:991.867 - 0.771ms returns FALSE
T39A4 001:991.908 JLINK_HasError()
T39A4 001:994.090 JLINK_IsHalted()
T39A4 001:994.843 - 0.782ms returns FALSE
T39A4 001:994.894 JLINK_HasError()
T39A4 001:999.089 JLINK_IsHalted()
T39A4 001:999.680 - 0.610ms returns FALSE
T39A4 001:999.715 JLINK_HasError()
T39A4 002:001.118 JLINK_IsHalted()
T39A4 002:001.722 - 0.645ms returns FALSE
T39A4 002:001.780 JLINK_HasError()
T39A4 002:003.099 JLINK_IsHalted()
T39A4 002:003.854 - 0.782ms returns FALSE
T39A4 002:003.899 JLINK_HasError()
T39A4 002:005.070 JLINK_IsHalted()
T39A4 002:005.832 - 0.789ms returns FALSE
T39A4 002:005.876 JLINK_HasError()
T39A4 002:007.067 JLINK_IsHalted()
T39A4 002:007.807 - 0.773ms returns FALSE
T39A4 002:007.861 JLINK_HasError()
T39A4 002:009.849 JLINK_IsHalted()
T39A4 002:010.600 - 0.777ms returns FALSE
T39A4 002:010.643 JLINK_HasError()
T39A4 002:012.810 JLINK_IsHalted()
T39A4 002:013.420 - 0.623ms returns FALSE
T39A4 002:013.447 JLINK_HasError()
T39A4 002:020.025 JLINK_IsHalted()
T39A4 002:020.777 - 0.782ms returns FALSE
T39A4 002:020.825 JLINK_HasError()
T39A4 002:022.026 JLINK_IsHalted()
T39A4 002:022.771 - 0.773ms returns FALSE
T39A4 002:022.816 JLINK_HasError()
T39A4 002:024.028 JLINK_IsHalted()
T39A4 002:024.776 - 0.774ms returns FALSE
T39A4 002:024.819 JLINK_HasError()
T39A4 002:026.774 JLINK_IsHalted()
T39A4 002:027.411 - 0.652ms returns FALSE
T39A4 002:027.439 JLINK_HasError()
T39A4 002:028.718 JLINK_IsHalted()
T39A4 002:029.317 - 0.609ms returns FALSE
T39A4 002:029.337 JLINK_HasError()
T39A4 002:030.853 JLINK_IsHalted()
T39A4 002:031.371 - 0.528ms returns FALSE
T39A4 002:031.391 JLINK_HasError()
T39A4 002:032.840 JLINK_IsHalted()
T39A4 002:033.371 - 0.541ms returns FALSE
T39A4 002:033.391 JLINK_HasError()
T39A4 002:035.018 JLINK_IsHalted()
T39A4 002:035.797 - 0.803ms returns FALSE
T39A4 002:035.834 JLINK_HasError()
T39A4 002:037.023 JLINK_IsHalted()
T39A4 002:037.792 - 0.792ms returns FALSE
T39A4 002:037.829 JLINK_HasError()
T39A4 002:039.015 JLINK_IsHalted()
T39A4 002:039.804 - 0.815ms returns FALSE
T39A4 002:039.849 JLINK_HasError()
T39A4 002:041.728 JLINK_IsHalted()
T39A4 002:042.270 - 0.560ms returns FALSE
T39A4 002:042.302 JLINK_HasError()
T39A4 002:043.704 JLINK_IsHalted()
T39A4 002:044.439 - 0.759ms returns FALSE
T39A4 002:044.479 JLINK_HasError()
T39A4 002:045.835 JLINK_IsHalted()
T39A4 002:046.620 - 0.810ms returns FALSE
T39A4 002:046.661 JLINK_HasError()
T39A4 002:049.988 JLINK_IsHalted()
T39A4 002:050.755 - 0.813ms returns FALSE
T39A4 002:050.801 JLINK_HasError()
T39A4 002:052.901 JLINK_IsHalted()
T39A4 002:053.676 - 0.805ms returns FALSE
T39A4 002:053.725 JLINK_HasError()
T39A4 002:054.947 JLINK_IsHalted()
T39A4 002:055.694 - 0.774ms returns FALSE
T39A4 002:055.738 JLINK_HasError()
T39A4 002:056.941 JLINK_IsHalted()
T39A4 002:057.675 - 0.761ms returns FALSE
T39A4 002:057.719 JLINK_HasError()
T39A4 002:058.949 JLINK_IsHalted()
T39A4 002:059.577 - 0.660ms returns FALSE
T39A4 002:059.627 JLINK_HasError()
T39A4 002:062.917 JLINK_IsHalted()
T39A4 002:063.660 - 0.770ms returns FALSE
T39A4 002:063.703 JLINK_HasError()
T39A4 002:064.920 JLINK_IsHalted()
T39A4 002:065.675 - 0.782ms returns FALSE
T39A4 002:065.723 JLINK_HasError()
T39A4 002:067.871 JLINK_IsHalted()
T39A4 002:068.608 - 0.776ms returns FALSE
T39A4 002:068.665 JLINK_HasError()
T39A4 002:072.870 JLINK_IsHalted()
T39A4 002:073.618 - 0.775ms returns FALSE
T39A4 002:073.661 JLINK_HasError()
T39A4 002:074.850 JLINK_IsHalted()
T39A4 002:075.402 - 0.568ms returns FALSE
T39A4 002:075.433 JLINK_HasError()
T39A4 002:079.980 JLINK_IsHalted()
T39A4 002:080.765 - 0.813ms returns FALSE
T39A4 002:080.806 JLINK_HasError()
T39A4 002:082.836 JLINK_IsHalted()
T39A4 002:083.595 - 0.785ms returns FALSE
T39A4 002:083.636 JLINK_HasError()
T39A4 002:086.879 JLINK_IsHalted()
T39A4 002:087.619 - 0.767ms returns FALSE
T39A4 002:087.661 JLINK_HasError()
T39A4 002:088.875 JLINK_IsHalted()
T39A4 002:089.640 - 0.791ms returns FALSE
T39A4 002:089.680 JLINK_HasError()
T39A4 002:090.860 JLINK_IsHalted()
T39A4 002:091.429 - 0.586ms returns FALSE
T39A4 002:091.465 JLINK_HasError()
T39A4 002:093.823 JLINK_IsHalted()
T39A4 002:094.576 - 0.780ms returns FALSE
T39A4 002:094.620 JLINK_HasError()
T39A4 002:095.839 JLINK_IsHalted()
T39A4 002:096.575 - 0.763ms returns FALSE
T39A4 002:096.619 JLINK_HasError()
T39A4 002:097.823 JLINK_IsHalted()
T39A4 002:098.506 - 0.709ms returns FALSE
T39A4 002:098.549 JLINK_HasError()
T39A4 002:101.786 JLINK_IsHalted()
T39A4 002:102.542 - 0.784ms returns FALSE
T39A4 002:102.588 JLINK_HasError()
T39A4 002:103.815 JLINK_IsHalted()
T39A4 002:104.577 - 0.789ms returns FALSE
T39A4 002:104.621 JLINK_HasError()
T39A4 002:106.807 JLINK_IsHalted()
T39A4 002:107.360 - 0.571ms returns FALSE
T39A4 002:107.394 JLINK_HasError()
T39A4 002:108.797 JLINK_IsHalted()
T39A4 002:109.598 - 0.829ms returns FALSE
T39A4 002:109.642 JLINK_HasError()
T39A4 002:114.750 JLINK_IsHalted()
T39A4 002:115.507 - 0.785ms returns FALSE
T39A4 002:115.553 JLINK_HasError()
T39A4 002:116.864 JLINK_IsHalted()
T39A4 002:117.646 - 0.809ms returns FALSE
T39A4 002:117.690 JLINK_HasError()
T39A4 002:119.494 JLINK_IsHalted()
T39A4 002:120.287 - 0.819ms returns FALSE
T39A4 002:120.330 JLINK_HasError()
T39A4 002:122.812 JLINK_IsHalted()
T39A4 002:123.423 - 0.631ms returns FALSE
T39A4 002:123.459 JLINK_HasError()
T39A4 002:129.779 JLINK_IsHalted()
T39A4 002:133.649   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 002:134.457 - 4.702ms returns TRUE
T39A4 002:134.494 JLINK_ReadReg(R15 (PC))
T39A4 002:134.509 - 0.018ms returns 0x20000000
T39A4 002:134.519 JLINK_ClrBPEx(BPHandle = 0x00000033)
T39A4 002:134.528 - 0.013ms returns 0x00
T39A4 002:134.539 JLINK_ReadReg(R0)
T39A4 002:134.547 - 0.012ms returns 0xD8BEEDE2
T39A4 002:135.849 JLINK_HasError()
T39A4 002:135.867 JLINK_WriteReg(R0, 0x00000003)
T39A4 002:135.878 - 0.014ms returns 0
T39A4 002:135.888 JLINK_WriteReg(R1, 0x08000000)
T39A4 002:135.897 - 0.012ms returns 0
T39A4 002:135.907 JLINK_WriteReg(R2, 0x00003998)
T39A4 002:135.915 - 0.012ms returns 0
T39A4 002:135.924 JLINK_WriteReg(R3, 0x04C11DB7)
T39A4 002:135.933 - 0.012ms returns 0
T39A4 002:135.942 JLINK_WriteReg(R4, 0x00000000)
T39A4 002:135.951 - 0.012ms returns 0
T39A4 002:135.960 JLINK_WriteReg(R5, 0x00000000)
T39A4 002:135.968 - 0.012ms returns 0
T39A4 002:135.978 JLINK_WriteReg(R6, 0x00000000)
T39A4 002:135.986 - 0.012ms returns 0
T39A4 002:135.996 JLINK_WriteReg(R7, 0x00000000)
T39A4 002:136.004 - 0.012ms returns 0
T39A4 002:136.014 JLINK_WriteReg(R8, 0x00000000)
T39A4 002:136.022 - 0.012ms returns 0
T39A4 002:136.032 JLINK_WriteReg(R9, 0x20000160)
T39A4 002:136.040 - 0.012ms returns 0
T39A4 002:136.050 JLINK_WriteReg(R10, 0x00000000)
T39A4 002:136.058 - 0.012ms returns 0
T39A4 002:136.067 JLINK_WriteReg(R11, 0x00000000)
T39A4 002:136.076 - 0.012ms returns 0
T39A4 002:136.085 JLINK_WriteReg(R12, 0x00000000)
T39A4 002:136.094 - 0.012ms returns 0
T39A4 002:136.103 JLINK_WriteReg(R13 (SP), 0x20001000)
T39A4 002:136.112 - 0.012ms returns 0
T39A4 002:136.122 JLINK_WriteReg(R14, 0x20000001)
T39A4 002:136.130 - 0.012ms returns 0
T39A4 002:136.140 JLINK_WriteReg(R15 (PC), 0x2000006A)
T39A4 002:136.148 - 0.012ms returns 0
T39A4 002:136.158 JLINK_WriteReg(XPSR, 0x01000000)
T39A4 002:136.166 - 0.012ms returns 0
T39A4 002:136.176 JLINK_WriteReg(MSP, 0x20001000)
T39A4 002:136.184 - 0.012ms returns 0
T39A4 002:136.194 JLINK_WriteReg(PSP, 0x20001000)
T39A4 002:136.202 - 0.012ms returns 0
T39A4 002:136.212 JLINK_WriteReg(CFBP, 0x00000000)
T39A4 002:136.220 - 0.012ms returns 0
T39A4 002:136.230 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T39A4 002:136.239 - 0.013ms returns 0x00000034
T39A4 002:136.250 JLINK_Go()
T39A4 002:136.268   CPU_ReadMem(4 bytes @ 0x********)
T39A4 002:140.189 - 3.959ms
T39A4 002:140.220 JLINK_IsHalted()
T39A4 002:143.723   CPU_ReadMem(2 bytes @ 0x20000000)
T39A4 002:144.480 - 4.296ms returns TRUE
T39A4 002:144.544 JLINK_ReadReg(R15 (PC))
T39A4 002:144.564 - 0.024ms returns 0x20000000
T39A4 002:144.579 JLINK_ClrBPEx(BPHandle = 0x00000034)
T39A4 002:144.590 - 0.015ms returns 0x00
T39A4 002:144.603 JLINK_ReadReg(R0)
T39A4 002:144.612 - 0.014ms returns 0x00000000
T39A4 002:199.966 JLINK_WriteMem(0x20000000, 0x2 Bytes, ...)
T39A4 002:199.998   Data:  FE E7
T39A4 002:200.030   CPU_WriteMem(2 bytes @ 0x20000000)
T39A4 002:200.802 - 0.861ms returns 0x2
T39A4 002:200.843 JLINK_HasError()
T39A4 002:200.858 JLINK_HasError()
T39A4 002:200.870 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T39A4 002:200.881 - 0.015ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T39A4 002:200.894 JLINK_Reset()
T39A4 002:200.917   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T39A4 002:201.487   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T39A4 002:207.184   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T39A4 002:210.565   Reset: Reset device via AIRCR.SYSRESETREQ.
T39A4 002:210.594   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T39A4 002:263.850   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T39A4 002:264.267   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T39A4 002:264.687   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T39A4 002:271.068   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T39A4 002:274.796   CPU_WriteMem(4 bytes @ 0x********)
T39A4 002:275.244   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T39A4 002:275.668   CPU_ReadMem(4 bytes @ 0x********)
T39A4 002:276.128 - 75.243ms
T39A4 002:276.151 JLINK_Go()
T39A4 002:276.169   CPU_ReadMem(4 bytes @ 0x********)
T39A4 002:276.653   CPU_WriteMem(4 bytes @ 0xE0002008)
T39A4 002:276.670   CPU_WriteMem(4 bytes @ 0xE000200C)
T39A4 002:276.681   CPU_WriteMem(4 bytes @ 0xE0002010)
T39A4 002:276.691   CPU_WriteMem(4 bytes @ 0xE0002014)
T39A4 002:276.701   CPU_WriteMem(4 bytes @ 0xE0002018)
T39A4 002:276.711   CPU_WriteMem(4 bytes @ 0xE000201C)
T39A4 002:279.131   CPU_WriteMem(4 bytes @ 0xE0001004)
T39A4 002:280.218 - 4.074ms
T39A4 002:286.305 JLINK_Close()
T39A4 002:286.608   CPU is running
T39A4 002:286.626   CPU_WriteMem(4 bytes @ 0xE0002008)
T39A4 002:287.124   CPU is running
T39A4 002:287.141   CPU_WriteMem(4 bytes @ 0xE000200C)
T39A4 002:287.680   CPU is running
T39A4 002:287.696   CPU_WriteMem(4 bytes @ 0xE0002010)
T39A4 002:288.181   CPU is running
T39A4 002:288.197   CPU_WriteMem(4 bytes @ 0xE0002014)
T39A4 002:289.003   CPU is running
T39A4 002:289.019   CPU_WriteMem(4 bytes @ 0xE0002018)
T39A4 002:289.562   CPU is running
T39A4 002:289.579   CPU_WriteMem(4 bytes @ 0xE000201C)
T39A4 002:295.214   OnDisconnectTarget() start
T39A4 002:295.230    J-Link Script File: Executing OnDisconnectTarget()
T39A4 002:295.243   CPU_WriteMem(4 bytes @ 0xE0042004)
T39A4 002:298.012   OnDisconnectTarget() end - Took 874us
T39A4 002:298.033   CPU_ReadMem(4 bytes @ 0x********)
T39A4 002:318.331 - 32.047ms
T39A4 002:318.357   
T39A4 002:318.364   Closed
