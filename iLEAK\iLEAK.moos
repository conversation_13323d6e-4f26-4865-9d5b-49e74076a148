//------------------------------------------------
// iLEAK config block

ProcessConfig = iLEAK
{
   AppTick   = 10
   CommsTick = 10

   RecvIP = "************"
   RecvPort =  8004

    // CAN2NET模块的IP
    DestIP = "*************"
    DestPort = 4004

    // BlueSocket重试机制配置
    max_retries = 5             // 最大重试次数
    retry_delay = 2             // 每次重试之间的延迟（秒）
}

//------------------------------------------------
// 设备控制示例命令
//
// 抛载控制示例：
// uPokeDB LOAD_ACTION=true        // 开启抛载
// uPokeDB LOAD_ACTION=false       // 关闭抛载
//
// 设备控制消息示例：
// uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=LOAD;Power=on;"
// uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=LOAD;Power=off;"
// uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=DVL;Power=on;"
// uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=ALL;Power=off;"

