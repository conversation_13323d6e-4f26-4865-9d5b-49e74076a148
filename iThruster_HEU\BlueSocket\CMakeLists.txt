cmake_minimum_required(VERSION 3.2)

project(BlueSocket)

# 开启所有Warning提示
add_compile_options(-Wall -Wextra)

# 指定编译器使用C/C++ 11
set(CMAKE_C_STANDARD_REQUIRED ON)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 11)

# 设置头文件搜索路径
include_directories(${PROJECT_SOURCE_DIR}/include)

# 设置静态链接库输出路径
set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/lib)

# 源码集合
set(SRC
${PROJECT_SOURCE_DIR}/src/BlueSocket.cpp)

# 生成静态链接库
add_library(BlueSocket ${SRC})
