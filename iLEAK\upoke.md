# LEAK模块 uPokeDB 命令参考文档

## 概述
本文档提供LEAK模块支持的所有uPokeDB命令，用于控制各种设备的上下电操作。

## 命令格式说明

### 方式1：LOAD_ACTION变量（仅限抛载控制）
```bash
uPokeDB LOAD_ACTION=true   # 开启抛载
uPokeDB LOAD_ACTION=false  # 关闭抛载
```

### 方式2：CONTROL_MSG变量（支持所有设备）
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=设备名;Power=on/off;"
```

## 抛载控制命令

### 开启抛载
```bash
# 方式1：使用LOAD_ACTION变量
uPokeDB LOAD_ACTION=true

# 方式2：使用CONTROL_MSG变量
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=LOAD;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x02,0x00,0x00,0x00,0xFA]`

### 关闭抛载
```bash
# 方式1：使用LOAD_ACTION变量
uPokeDB LOAD_ACTION=false

# 方式2：使用CONTROL_MSG变量
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=LOAD;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 测速仪(DVL)控制命令

### DVL上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=DVL;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x01,0x00,0x00,0x00,0xFA]`

### DVL下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=DVL;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 超短基线定位(USBL)控制命令

### USBL上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=USBL;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x01,0x00,0x00,0x00,0x00,0xFA]`

### USBL下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=USBL;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 频闪灯(LED)控制命令

### 频闪灯上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=LED;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x02,0x00,0x00,0x00,0x00,0xFA]`

### 频闪灯下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=LED;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 水声通讯(HYP)控制命令

### 水声通讯上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=HYP;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x02,0x00,0x00,0x00,0x00,0x00,0xFA]`

### 水声通讯下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=HYP;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 避障声呐(AVOID)控制命令

### 避障声呐上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=AVOID;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x01,0x00,0x00,0xFA]`

### 避障声呐下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=AVOID;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 载荷控制命令

### 载荷高压上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=AEMLOADHIGH;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x01,0x00,0xFA]`

### 载荷高压下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=AEMLOADHIGH;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

### 载荷低压上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=AEMLOADLOW;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x01,0xFA]`

### 载荷低压下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=AEMLOADLOW;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 组合设备控制命令

### 所有设备上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=ALL;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x03,0x03,0x01,0x01,0x01,0x01,0xFA]`

### 所有设备下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=ALL;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## CAN报文格式说明

### 13字节网络帧结构
```
[0x08, 0x00, 0x00, 0x03, 0x21, 0xFA, COM4, COM2, COM6, COM9, HIGH, LOW, 0xFA]
 |     |     |     |     |     |     |     |     |     |     |     |     |
 |     |     |     |     |     |     |     |     |     |     |     |     帧尾标识
 |     |     |     |     |     |     |     |     |     |     |     载荷低压控制
 |     |     |     |     |     |     |     |     |     |     载荷高压控制
 |     |     |     |     |     |     |     |     |     COM9控制(AVOID)
 |     |     |     |     |     |     |     |     COM6控制(DVL|LOAD)
 |     |     |     |     |     |     |     COM2控制(USBL|LED)
 |     |     |     |     |     |     COM4控制(PPB|HYP)
 |     |     |     |     |     帧头标识(0xFA)
 |     |     |     |     CAN ID (0x321)
 |     |     |     网络帧头
 网络帧长度标识
```

### 设备值映射表
| 设备 | COM口 | 开启值 | 关闭值 | 在帧中位置 |
|------|-------|--------|--------|------------|
| PPB | COM4 | 0x01 | 0x00 | Frame[6] |
| HYP | COM4 | 0x02 | 0x00 | Frame[6] |
| USBL | COM2 | 0x01 | 0x00 | Frame[7] |
| LED | COM2 | 0x02 | 0x00 | Frame[7] |
| DVL | COM6 | 0x01 | 0x00 | Frame[8] |
| LOAD | COM6 | 0x02 | 0x00 | Frame[8] |
| AVOID | COM9 | 0x01 | 0x00 | Frame[9] |
| AEMLOADHIGH | - | 0x01 | 0x00 | Frame[10] |
| AEMLOADLOW | - | 0x01 | 0x00 | Frame[11] |

## 使用注意事项

1. **LOAD_ACTION变量**仅支持抛载控制，其他设备必须使用CONTROL_MSG
2. **CONTROL_MSG格式**必须严格按照`MsgType=control;Act=sensor;Type=设备名;Power=on/off;`
3. **设备名称**区分大小写，必须使用大写字母
4. **同一COM口多设备**：如果同时开启同一COM口的多个设备，控制值会进行OR运算
5. **ALL命令**：会控制除LOAD外的所有设备，LOAD需要单独控制

## 状态查询

LEAK模块会发布以下MOOS变量供状态查询：
- `LOAD_STATUS`：抛载状态
- `DEVICE_CONTROL_RECEIVED`：设备控制命令接收确认
- `LEAK`：综合漏水状态
- `LEAK_CODE`：漏水故障码
