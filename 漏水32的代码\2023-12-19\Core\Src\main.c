/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : main.c
 * @brief          : Main program body
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2023 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "can.h"
#include "i2c.h"
#include "usart.h"
#include "gpio.h"
/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

#include "SHT30.h"
#include "string.h"
#include "MS5611.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */
#include "stdio.h"
// 重写printf
int fputc(int ch, FILE *f)
{
    uint8_t temp[1] = {ch};
    HAL_UART_Transmit(&huart1, temp, 1, 5000);

    return ch;
}
/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
extern CAN_HandleTypeDef hcan;
uint8_t dealdatatime;
CAN_TxHeaderTypeDef TxHeader;
CAN_TxHeaderTypeDef TxHeader2;
uint8_t TxData[8];
uint8_t TxData2[8];
uint32_t TxMailbox;
uint32_t TxMailbox2;
uint32_t std_id;
uint32_t std_id2;
CAN_RxHeaderTypeDef RxHeader;
uint8_t TxData[8] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x08, 0x08, 0x09};
uint8_t TxData2[8] = {0x08, 0x07, 0x06, 0x05, 0x04, 0x03, 0x02, 0x01};
uint8_t RxData[8];
uint32_t TxMailbox;
uint32_t TxMailbox2;
uint32_t RxFifo;
uint32_t std_id = 0x123;
uint32_t std_id2 = 0x321;
uint8_t rec_buf[1];

uint8_t RxDatalen = 0;
uint8_t Leakbuf[8] = {0xFB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}; // 漏水检测报文 最后一位为SUM
uint8_t Leak = 0x00;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
void print_package(uint8_t *package, size_t package_size) // 数组打印
{
    for (size_t i = 0; i < package_size; i++)
    {
        printf("%02x", package[i]);
    }
    printf("\n\r");
}

void Powerswitch(uint8_t *buf) // 上下电开关
{
    // com4和com3关
    if (buf[1] == 0x00)
    {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_0, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, GPIO_PIN_RESET);
    }
    // com4单开
    else if (buf[1] == 0x01)
    {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_0, GPIO_PIN_SET);
    }
    // com4单关
    else if (buf[1] == 0x02)
    {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_0, GPIO_PIN_RESET);
    }
    // com3单开
    else if (buf[1] == 0x03)
    {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, GPIO_PIN_SET);
    }
    // com3单关
    else if (buf[1] == 0x04)
    {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, GPIO_PIN_RESET);
    }
    // com4和com3开
    else if (buf[1] == 0x05)
    {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_0, GPIO_PIN_SET);
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, GPIO_PIN_SET);
    }

    // com2和com8关
    if (buf[2] == 0x00)
    {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_2, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_3, GPIO_PIN_RESET);
    }
    // com2单开
    else if (buf[2] == 0x01)
    {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_2, GPIO_PIN_SET);
    }
    // com2单关
    else if (buf[2] == 0x02)
    {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_2, GPIO_PIN_RESET);
    }
    // com8单开
    else if (buf[2] == 0x03)
    {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_3, GPIO_PIN_SET);
    }
    // com8单关
    else if (buf[2] == 0x04)
    {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_3, GPIO_PIN_RESET);
    }
    // com2和com8开
    else if (buf[2] == 0x05)
    {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_2, GPIO_PIN_SET);
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_3, GPIO_PIN_SET);
    }

    // com6和com5关
    if (buf[3] == 0x00)
    {
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_6, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_7, GPIO_PIN_RESET);
    }
    // com6单开
    else if (buf[3] == 0x01)
    {
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_6, GPIO_PIN_SET);
    }
    // com6单关
    else if (buf[3] == 0x02)
    {
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_6, GPIO_PIN_RESET);
    }
    // com5单开
    else if (buf[3] == 0x03)
    {
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_7, GPIO_PIN_SET);
    }
    // com5单关
    else if (buf[3] == 0x04)
    {
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_7, GPIO_PIN_RESET);
    }
    // com6和com5开
    else if (buf[3] == 0x05)
    {
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_6, GPIO_PIN_SET);
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_7, GPIO_PIN_SET);
    }

    // com9和com1关
    if (buf[4] == 0x00)
    {
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_RESET);
    }
    // com9单开
    else if (buf[4] == 0x01)
    {
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, GPIO_PIN_SET);
    }
    // com9单关
    else if (buf[4] == 0x02)
    {
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, GPIO_PIN_RESET);
    }
    // com1单开
    else if (buf[4] == 0x03)
    {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_SET);
    }
    // com1单关
    else if (buf[4] == 0x04)
    {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_RESET);
    }
    // com9和com1开
    else if (buf[4] == 0x05)
    {
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, GPIO_PIN_SET);
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_SET);
    }
}

// 漏水检测
void WaterDetect()
{
    Leak = 0x00;
    Leakbuf[0] = 0xFB;
    if (HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_0) == RESET) // 六个漏水检测
    {
        //  printf("DEC1:ERROR\n");
        Leakbuf[1] = 0x01;
    }
    else
    {
        // printf("DEC1:OK\n");
        Leakbuf[1] = 0x00;
    }

    if (HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_1) == RESET)
    {
        // printf("DEC2:ERROR\n");
        Leakbuf[2] = 0x02;
    }
    else
    {
        // printf("DEC2:OK\n");
        Leakbuf[2] = 0x00;
    }

    if (HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_2) == RESET)
    {
        // printf("DEC3:ERROR\n");
        Leakbuf[3] = 0x04;
    }
    else
    {
        // printf("DEC3:OK\n");
        Leakbuf[3] = 0x00;
    }

    if (HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_3) == RESET)
    {
        // printf("DEC4:ERROR\n");
        Leakbuf[4] = 0x08;
    }
    else
    {
        // printf("DEC4:OK\n");
        Leakbuf[4] = 0x00;
    }

    if (HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_4) == RESET)
    {
        // printf("DEC5:ERROR\n");
        Leakbuf[5] = 0x16;
    }
    else
    {
        // printf("DEC5:OK\n");
        Leakbuf[5] = 0x00;
    }

    if (HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_5) == RESET)
    {
        // printf("DEC6:ERROR\n");
        Leakbuf[6] = 0x32;
    }
    else
    {
        // printf("DEC6:OK\n");
        Leakbuf[6] = 0x00;
    }
    for (int i = 1; i <= 6; i++)
    {
        Leak += Leakbuf[i];
    }
    Leakbuf[7] = Leak;
    HAL_CAN_AddTxMessage(&hcan, &TxHeader, Leakbuf, &TxMailbox);
}

int main()
{
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_CAN_Init();
    MX_I2C1_Init();
    MX_I2C2_Init();
    MX_USART1_UART_Init();
    HAL_CAN_Start(&hcan);
    TxHeader.RTR = CAN_RTR_DATA;
    TxHeader.IDE = CAN_ID_STD;
    TxHeader.StdId = std_id;
    TxHeader.TransmitGlobalTime = DISABLE;
    TxHeader.DLC = 8;
    TxHeader2.RTR = CAN_RTR_DATA;
    TxHeader2.IDE = CAN_ID_STD;
    TxHeader2.StdId = std_id2;
    TxHeader2.TransmitGlobalTime = DISABLE;
    TxHeader2.DLC = 8;
    // 配置滤波器
    CAN_FilterTypeDef filterConfig;
    filterConfig.FilterIdHigh = 0x0321 << 5;
    filterConfig.FilterIdLow = 0x0000;
    filterConfig.FilterMaskIdHigh = 0xFFFF << 5;
    filterConfig.FilterMaskIdLow = 0x0000;
    filterConfig.FilterFIFOAssignment = CAN_FILTER_FIFO0;
    filterConfig.FilterBank = 0;
    filterConfig.FilterMode = CAN_FILTERMODE_IDMASK;
    filterConfig.FilterScale = CAN_FILTERSCALE_32BIT;
    filterConfig.FilterActivation = ENABLE;
    HAL_CAN_ConfigFilter(&hcan, &filterConfig);
    MS5637_INIT();
    HAL_UART_Receive_IT(&huart1, rec_buf, 1);
    while (1)
    {
        HAL_Delay(500);
        HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
        // 设备上下电
        HAL_CAN_GetRxMessage(&hcan, CAN_RX_FIFO0, &RxHeader, RxData); // FA 00 00 00 00 00 00 00 00 FA

        RxDatalen = sizeof(RxData);
        if (RxDatalen != 0)
        {
            if ((RxData[0] == 0xFA) && (RxData[7] == 0xFA) && (RxHeader.StdId == 0x321))
            {
                Powerswitch(RxData);
                size_t package_size = RxDatalen / sizeof(uint8_t);
                print_package(RxData, package_size); // 在ttl串口打印上下电报文
            }
            RxDatalen = 0;
        }
        // 漏水检测
        WaterDetect();
    }
}

/**
 * @brief  The application entry point.
 * @retval int
 */

/**
 * @brief System Clock Configuration
 * @retval None
 */
void SystemClock_Config(void)
{
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

    /** Initializes the RCC Oscillators according to the specified parameters
     * in the RCC_OscInitTypeDef structure.
     */
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
    RCC_OscInitStruct.HSIState = RCC_HSI_ON;
    RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
    if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
    {
        Error_Handler();
    }

    /** Initializes the CPU, AHB and APB buses clocks
     */
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK | RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSI;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
    RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
    {
        Error_Handler();
    }
}

/* USER CODE BEGIN 4 */
// void HAL_UART_RxCpltCallback(UART_HandleTypeDef *UARTHandle)
//{

//    if (UARTHandle->Instance == USART1)
//    {
//        if (rec_buf[0] == 0x01)//串口中断接收到0X01，关闭所有继电器
//        {
//            printf("RESET");
//            HAL_GPIO_WritePin(GPIOA, GPIO_PIN_15, GPIO_PIN_RESET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_3, GPIO_PIN_RESET);
//            HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, GPIO_PIN_RESET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_15, GPIO_PIN_RESET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_14, GPIO_PIN_RESET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_RESET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_RESET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_2, GPIO_PIN_RESET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, GPIO_PIN_RESET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_0, GPIO_PIN_RESET);
//            HAL_GPIO_WritePin(GPIOA, GPIO_PIN_7, GPIO_PIN_RESET);
//            HAL_GPIO_WritePin(GPIOA, GPIO_PIN_6, GPIO_PIN_RESET);
//        }
//        if (rec_buf[0] == 0x02)////串口中断接收到0X01，打开所有继电器
//        {
//            printf("SET");
//            HAL_GPIO_WritePin(GPIOA, GPIO_PIN_15, GPIO_PIN_SET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_3, GPIO_PIN_SET);
//            HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, GPIO_PIN_SET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_15, GPIO_PIN_SET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_14, GPIO_PIN_SET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_SET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_SET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_2, GPIO_PIN_SET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, GPIO_PIN_SET);
//            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_0, GPIO_PIN_SET);
//            HAL_GPIO_WritePin(GPIOA, GPIO_PIN_7, GPIO_PIN_SET);
//            HAL_GPIO_WritePin(GPIOA, GPIO_PIN_6, GPIO_PIN_SET);
//        }
//    }
//    HAL_UART_Receive_IT(&huart1, rec_buf, 1);
//}
/* USER CODE END 4 */

/**
 * @brief  This function is executed in case of error occurrence.
 * @retval None
 */
void Error_Handler(void)
{
    /* USER CODE BEGIN Error_Handler_Debug */
    /* User can add his own implementation to report the HAL error return state */
    __disable_irq();
    while (1)
    {
    }
    /* USER CODE END Error_Handler_Debug */
}

#ifdef USE_FULL_ASSERT
/**
 * @brief  Reports the name of the source file and the source line number
 *         where the assert_param error has occurred.
 * @param  file: pointer to the source file name
 * @param  line: assert_param error line source number
 * @retval None
 */
void assert_failed(uint8_t *file, uint32_t line)
{
    /* USER CODE BEGIN 6 */
    /* User can add his own implementation to report the file name and line number,
       ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
    /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
