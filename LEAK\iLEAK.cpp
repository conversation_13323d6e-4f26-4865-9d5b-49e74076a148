/************************************************************/
/*    NAME: zhaoqinchao                                    */
/*    ORGN: HEU                                             */
/*    FILE: iLEAK.cpp                                      */
/*    DATE: 2025/07/18                                      */
/************************************************************/

#include <iterator>
#include <pthread.h>
#include <cstdlib>
#include <cerrno>
#include <cstring>
#include <algorithm>
#include <vector>
#include <string>
#include "MBUtils.h"
#include "iLEAK.h"

using namespace std;
bool iLEAK::OnStartUp()
{
    string sRecvIP; // 从配置文件中读取 接收IP    本机监听地址     3.5版本 ************   现在************
    if (!m_MissionReader.GetConfigurationParam("RecvIP", sRecvIP))
    {
        MOOSTrace("cannot get RecvIP \n");
        sRecvIP = "0.0.0.0";
    }

    int iRecvPort; // 从配置文件中读取 接收端口
    if (!m_MissionReader.GetConfigurationParam("RecvPort", iRecvPort))
    {
        MOOSTrace("cannot get RecvPort \n");
        return false;
    }

    string sDestIP;                                                // 从配置文件中读取 目标IP 0.96     本机监听地址can网卡，can网卡接收到数据后，can网卡将数据发送给can网络上的其他设备
    if (!m_MissionReader.GetConfigurationParam("DestIP", sDestIP)) // 从配置文件中读取参数，如果读取失败则存储到变量中，如果读取失败则返回
    {
        MOOSTrace("cannot get DestIP \n");
        return false;
    }

    int iDestPort; // 从配置文件中读取 目标端口
    if (!m_MissionReader.GetConfigurationParam("DestPort", iDestPort))
    {
        MOOSTrace("cannot get DestPort \n");
        return false;
    }

    // 读取容错机制配置参数
    if (!m_MissionReader.GetConfigurationParam("max_retries", m_max_retries))
    {
        MOOSTrace("cannot get max_retries, using default: %d \n", m_max_retries);
    }

    if (!m_MissionReader.GetConfigurationParam("retry_delay", m_retry_delay))
    {
        MOOSTrace("cannot get retry_delay, using default: %d \n", m_retry_delay);
    }

    // 使用容错机制打开接收套接字
    if (!RecvSock.OpenSocketWithRetry(sRecvIP, iRecvPort, m_max_retries, m_retry_delay))
    {
        MOOSTrace("Failed to open RecvSocket after %d retries \n", m_max_retries);
        return false;
    }

    // 绑定接收套接字，失败时重试
    if (!RecvSock.BindSocket())
    {
        MOOSTrace("Failed to bind RecvSocket, attempting rebind with retry \n");
        if (!RecvSock.RebindSocket(m_max_retries, m_retry_delay))
        {
            MOOSTrace("Failed to rebind RecvSocket after %d retries \n", m_max_retries);
            return false;
        }
    }

    // 使用容错机制打开发送套接字
    if (!SendSock.OpenSocketWithRetry(sDestIP, iDestPort, m_max_retries, m_retry_delay))
    {
        MOOSTrace("Failed to open SendSocket after %d retries \n", m_max_retries);
        return false;
    }

    // 启动接收线程
    m_thread_running = true;
    if (pthread_create(&m_recv_thread, NULL, iLEAK::RecvFrameWrapper, this) != 0) // 如果 pthread_create 函数返回0，则表示创建成功
    {
        MOOSTrace("[LEAK] Failed to create recv thread\n");
        m_thread_running = false;
        return false;
    }

    MOOSTrace("[LEAK] Recv thread created successfully\n");

    // 添加漏水检测MOOS变量
    AddMOOSVariable("LEAK", "LEAK", "", 0.1);

    m_timewarp = GetMOOSTimeWarp();

    RegisterVariables();
    return (true);
}

//---------------------------------------------------------
// Procedure: RegisterVariables

void iLEAK::RegisterVariables()
{
    // 注册监听LEAK1变量，用于接收iThruster_HEU模块的尾端漏水检测数据
    Register("LEAK1", 0);
    MOOSTrace("[LEAK] 已注册监听LEAK1变量\n");

    // 注册监听设备控制变量
    Register("LOAD_ACTION", 0);
    Register("CONTROL_MSG", 0);
    MOOSTrace("[LEAK] 已注册监听LOAD_ACTION和CONTROL_MSG变量\n");
}
//---------------------------------------------------------
// Procedure: OnNewMail
// 处理新邮件的回调函数，继承自MOOSApp基类的虚函数
bool iLEAK::OnNewMail(MOOSMSG_LIST &NewMail)
{
    // MOOSTrace("This is OnNewMail\n");
    UpdateLocalMOOSVariables(NewMail);
    // MOOSTrace("NewMail Size = %d\n", NewMail.size());

    // 处理接收到的MOOS变量
    MOOSMSG_LIST::iterator p;
    for (p = NewMail.begin(); p != NewMail.end(); p++)
    {
        CMOOSMsg &rMsg = *p;

        // 处理LEAK1变量（来自iThruster_HEU模块的尾端漏水检测）
        if (rMsg.GetKey() == "LEAK1")
        {
            m_leak1_status = rMsg.GetDouble();
            MOOSTrace("[LEAK] 接收到LEAK1变量: %.0f (0=正常, 64=尾端漏水故障)\n", m_leak1_status);
            // 接收到LEAK1变量后，立即发布综合漏水状态
            PublishCombinedLeakStatus();
        }

        // 处理LOAD_ACTION变量（抛载控制）
        else if (rMsg.GetKey() == "LOAD_ACTION")
        {
            m_load_action = rMsg.GetDouble() > 0; // 转换为布尔值
            MOOSTrace("[LEAK] 接收到LOAD_ACTION变量: %s\n", m_load_action ? "true" : "false");
            HandleLoadAction(m_load_action);
        }

        // 处理CONTROL_MSG变量（设备控制消息）
        else if (rMsg.GetKey() == "CONTROL_MSG")
        {
            m_control_msg = rMsg.GetString();
            MOOSTrace("[LEAK] 接收到CONTROL_MSG变量: %s\n", m_control_msg.c_str());
            HandleControlMessage(m_control_msg);
        }
    }

    // 返回 true 表示处理成功
    return (true);
}

//---------------------------------------------------------
// 静态线程包装函数 - 类的静态成员函数
void *iLEAK::RecvFrameWrapper(void *arg)
{
    iLEAK *instance = static_cast<iLEAK *>(arg);
    instance->RecvFrame();
    return nullptr;
}

//---------------------------------------------------------
// Constructor

iLEAK::iLEAK()
{
    m_iterations = 0;
    m_timewarp = 1;
    SetAppFreq(10); // 设置应用程序和通信频率
    SetCommsFreq(10);

    // 容错机制参数初始化
    m_max_retries = 5; // 默认最大重试次数
    m_retry_delay = 2; // 默认重试延迟2秒

    // 初始化线程控制变量
    m_thread_running = false;
    m_recv_thread = 0;

    // 初始化漏水检测变量
    m_leak_detected = false;
    m_leak_checksum = 0;
    m_calculated_checksum = 0;
    m_leak1_status = 0.0; // 初始化LEAK1变量
    for (int i = 0; i < 6; i++)
    {
        m_sensor_status[i] = false;
    }

    // 初始化设备控制变量
    m_load_action = false;
    m_control_msg = "";
    m_control_frame_ready = false;
    for (int i = 0; i < 10; i++)
    {
        m_device_control[i] = 0x00; // 所有设备默认关闭
    }
}

//---------------------------------------------------------
iLEAK::~iLEAK()
{
    // 停止接收线程
    m_thread_running = false;
    if (m_recv_thread != 0)
    {
        pthread_join(m_recv_thread, NULL);
    }
}

void iLEAK::RecvFrame() // 接收网络数据帧并转换为 CAN 帧进行处理
{
    MOOSTrace("[LEAK] RecvFrame thread started\n");

    // 循环接收网络数据帧，直到线程停止标志被设置
    while (m_thread_running)
    {
        // 定义数据帧
        vector<uint8_t> Frame;
        // 从网络套接字接收二进制数据到Frame中
        int n = RecvSock.RecvBinary(Frame, FRAME_LEN);

        // 不直接退出，而是重试
        if (n <= 0)
        {
            MOOSTrace("[LEAK] RecvFrame warning: n=%d, errno=%d (%s)\n",
                      n, errno, strerror(errno));
            MOOSPause(100); // 短暂延迟后继续
            continue;
        }

        // 成功接收到数据，解析帧
        ParseFrame(Frame);

        // MOOSTrace("FRAME %d  %d %d %d  %d %d %d  %d %d %d  %d %d %d  \n",Frame[0],Frame[1],Frame[2],Frame[3],Frame[4],Frame[5],Frame[6],Frame[7],Frame[8],Frame[9],Frame[10],Frame[11],Frame[12]);
    }

    MOOSTrace("接收的线程停止了\n");
}
// 解析接收到的网络数据帧，根据帧头类型分发到相应的处理函数，并发布到 MOOS 数据库
void iLEAK::ParseFrame(vector<uint8_t> Frame)
{
    // 验证数据帧长度是否为标准的13字节
    if (Frame.size() != FRAME_LEN)
    {
        MOOSTrace("invalid frame length \n");
        return;
    }

    vector<uint8_t> FrameHeader(Frame.begin(), Frame.begin() + 5); // 提取数据帧的前 5 个字节(索引0到4)作为帧头存储到FrameHeader中

    // 定义帧头类型
    vector<uint8_t> LeakDetectHeader = {0x08, 0x00, 0x00, 0x01, 0x23}; // 漏水检测数据 (CAN ID: 0x123)
    vector<uint8_t> ControlHeader = {0x08, 0x00, 0x00, 0x03, 0x21};    // 设备控制数据 (CAN ID: 0x321)

    // 检查是否为漏水检测数据帧 (CAN ID: 0x123)
    if (FrameHeader == LeakDetectHeader)
    {
        // 额外验证：检查数据帧的第6字节是否为0xFB
        if (Frame.size() >= 6 && Frame[5] == 0xFB)
        {
            MOOSTrace("[LEAK] Received leakage detection frame (CAN ID: 0x123)\n");
            ParseLeakageFrame(Frame);
        }
        else
        {
            MOOSTrace("[LEAK] Warning: Frame with CAN ID 0x123 but invalid data identifier (0x%02X)\n",
                      Frame.size() >= 6 ? Frame[5] : 0x00);
        }
        return;
    }

    // 检查是否为设备控制数据帧 (CAN ID: 0x321)
    if (FrameHeader == ControlHeader)
    {
        // 额外验证：检查数据帧的第6字节是否为0xFA
        if (Frame.size() >= 6 && Frame[5] == 0xFA)
        {
            MOOSTrace("[LEAK] Received device control frame (CAN ID: 0x321)\n");
            ParseControlFrame(Frame);
        }
        else
        {
            MOOSTrace("[LEAK] Warning: Frame with CAN ID 0x321 but invalid data identifier (0x%02X)\n",
                      Frame.size() >= 6 ? Frame[5] : 0x00);
        }
        return;
    }

    MOOSTrace("[LEAK] 未识别的数据帧格式\n");
}

// 解析漏水检测数据帧
void iLEAK::ParseLeakageFrame(vector<uint8_t> Frame)
{
    // 验证数据帧长度 (13字节网络帧)
    if (Frame.size() != FRAME_LEN)
    {
        MOOSTrace("[LEAK] Invalid frame length: %d, expected: %d\n", Frame.size(), FRAME_LEN);
        return;
    }
    // 完整CAN ID = (Frame[1]<<24) | (Frame[2]<<16) | (Frame[3]<<8) | Frame[4]
    uint32_t received_can_id = (Frame[1] << 24) | (Frame[2] << 16) | (Frame[3] << 8) | Frame[4];
    if (received_can_id != 0x123)
    {
        MOOSTrace("[LEAK] Invalid CAN ID: 0x%08X, expected: 0x123\n", received_can_id);
        return;
    }

    // 检查漏水数据标识符 (第6字节应为0xFB) - 对应漏水32代码中的Leakbuf[0]=0xFB
    if (Frame[5] != 0xFB)
    {
        MOOSTrace("[LEAK] Invalid leakage frame identifier: 0x%02X, expected: 0xFB\n", Frame[5]);
        return;
    }

    // 提取传感器数据 (字节6-11) - 对应漏水32代码中的Leakbuf[1-6]
    uint8_t sensor_data[6];
    for (int i = 0; i < 6; i++)
    {
        sensor_data[i] = Frame[6 + i];
    }

    // 提取接收到的校验和 (第13字节) - 对应漏水32代码中的Leakbuf[7]
    m_leak_checksum = Frame[12];

    // 计算校验和 (累加和) - 对应漏水32代码中的for(int i=1;i<=6;i++) Leak+=Leakbuf[i]
    m_calculated_checksum = 0;
    for (int i = 0; i < 6; i++)
    {
        m_calculated_checksum += sensor_data[i];
    }

    // 验证校验和
    if (m_leak_checksum != m_calculated_checksum)
    {
        MOOSTrace("[LEAK] Checksum error: received=0x%02X, calculated=0x%02X\n",
                  m_leak_checksum, m_calculated_checksum);
        return;
    }

    // 校验通过，构造8字节CAN数据帧并发送
    // 构造13字节网络帧：[08 00 00 01 23] + [FB + 6字节传感器数据 + 校验和]
    vector<uint8_t> network_frame = {0x08, 0x00, 0x00, 0x01, 0x23, // 网络帧头 + CAN ID 0x123
                                     0xFB, sensor_data[0], sensor_data[1],
                                     sensor_data[2], sensor_data[3], sensor_data[4],
                                     sensor_data[5], m_leak_checksum};

    // 通过BlueSocket发送13字节网络帧到CAN网络
    SendFrame(network_frame);
    MOOSTrace("[LEAK] Network frame sent: [0x08,0x00,0x00,0x01,0x23,0xFB,0x%02X,0x%02X,0x%02X,0x%02X,0x%02X,0x%02X,0x%02X]\n",
              sensor_data[0], sensor_data[1], sensor_data[2],
              sensor_data[3], sensor_data[4], sensor_data[5], m_leak_checksum);

    // 解析传感器状态
    // 传感器1: 0x01, 传感器2: 0x02, 传感器3: 0x04, 传感器4: 0x08, 传感器5: 0x16, 传感器6: 0x32
    uint8_t expected_values[6] = {0x01, 0x02, 0x04, 0x08, 0x16, 0x32};
    m_leak_detected = false;

    for (int i = 0; i < 6; i++)
    {
        // 传感器漏水时值为expected_values[i]，正常时为0x00
        m_sensor_status[i] = (sensor_data[i] == expected_values[i]);
        if (m_sensor_status[i])
        {
            m_leak_detected = true;
        }
    }

    // 发布综合漏水状态（本地传感器 OR 尾端漏水）
    PublishCombinedLeakStatus();

    // 详细调试输出 - 增强可调试性
    MOOSTrace("[LEAK] Frame parsed successfully:\n");
    MOOSTrace("[LEAK]   CAN ID: 0x123, Data ID: 0xFB\n");
    MOOSTrace("[LEAK]   Sensor data: [0x%02X,0x%02X,0x%02X,0x%02X,0x%02X,0x%02X]\n",
              sensor_data[0], sensor_data[1], sensor_data[2],
              sensor_data[3], sensor_data[4], sensor_data[5]);
    MOOSTrace("[LEAK]   Checksum: received=0x%02X, calculated=0x%02X\n",
              m_leak_checksum, m_calculated_checksum);
    MOOSTrace("[LEAK]   Status: Overall=%s, Sensors=[%d,%d,%d,%d,%d,%d]\n",
              m_leak_detected ? "LEAK_DETECTED" : "ALL_OK",
              m_sensor_status[0], m_sensor_status[1], m_sensor_status[2],
              m_sensor_status[3], m_sensor_status[4], m_sensor_status[5]);
}

//---------------------------------------------------------
// Procedure: OnConnectToServer
// 连接到MOOS服务器时的回调函数
bool iLEAK::OnConnectToServer()
{
    // register for variables here
    // possibly look at the mission file?
    // m_MissionReader.GetConfigurationParam("Name", <string>);
    // m_Comms.Register("VARNAME", 0);

    RegisterVariables();
    return (true);
}

//---------------------------------------------------------
// Procedure: Iterate()
//            happens AppTick times per second

bool iLEAK::Iterate()
{
    // 漏水检测模块主要通过接收线程处理数据
    // 此函数保持简单的心跳功能
    m_iterations++;

    // 检查是否有待发送的控制帧
    if (m_control_frame_ready)
    {
        SendControlFrame();
    }

    return (true);
}

void iLEAK::SendFrame(vector<uint8_t> Frame)
{
    int a = SendSock.SendBinary(Frame);
    // MOOSTrace ("a = %d \n",a);
    m_iterations++;
    // MOOSTrace ("iterate = %d \n",iterate);
}

// 发布综合漏水状态 - 本地传感器状态与LEAK1状态"或"运算后发布
void iLEAK::PublishCombinedLeakStatus()
{
    // 综合漏水状态：本地传感器检测 OR LEAK1（尾端漏水）
    bool overall_leak_detected = m_leak_detected || (m_leak1_status > 0);

    // 计算综合漏水故障码 - 传感器1-6 + 传感器7(尾端)=0x64
    double leak_code = 0;
    for (int i = 0; i < 6; i++)
    {
        if (m_sensor_status[i])
        {
            leak_code += (i + 1); // 传感器1=1, 传感器2=2, ..., 传感器6=6
        }
    }
    if (m_leak1_status > 0)
    {
        leak_code += 64.0; // 传感器7(尾端)=64
    }

    // 发布综合漏水状态到MOOS数据库
    Notify("LEAK", overall_leak_detected);
    Notify("LEAK_CODE", leak_code);

    // 详细状态发布
    Notify("LEAK_SENSORS", m_leak_detected); // 本地传感器状态
    Notify("LEAK_TAIL", m_leak1_status);     // 尾端漏水状态

    MOOSTrace("[LEAK] 综合漏水状态发布: 总体=%s, 故障码=%.0f, 传感器=%s, 尾端=%.0f\n",
              overall_leak_detected ? "LEAK_DETECTED" : "ALL_OK",
              leak_code,
              m_leak_detected ? "LEAK" : "OK",
              m_leak1_status);
}

// 处理LOAD_ACTION变量 - 抛载控制
void iLEAK::HandleLoadAction(bool action)
{
    // 设置抛载控制状态：true=开启(0x02), false=关闭(0x00)
    m_device_control[6] = action ? 0x02 : 0x00;
    m_control_frame_ready = true;

    MOOSTrace("[LEAK] 抛载控制: %s (设置data[6]=0x%02X)\n",
              action ? "开启" : "关闭", m_device_control[6]);

    // 立即发送控制帧
    SendControlFrame();
}

// 处理CONTROL_MSG变量 - 解析设备控制消息
void iLEAK::HandleControlMessage(std::string msg)
{
    // 解析CONTROL_MSG格式：MsgType=control;Act=sensor;Type=XXX;Power=on/off;
    vector<string> params;
    string remaining = msg;
    string param;

    // 按分号分割参数
    while (!remaining.empty())
    {
        size_t pos = remaining.find(';');
        if (pos != string::npos)
        {
            param = remaining.substr(0, pos);
            remaining = remaining.substr(pos + 1);
        }
        else
        {
            param = remaining;
            remaining = "";
        }

        if (!param.empty())
        {
            params.push_back(param);
        }
    }

    // 解析参数
    string msgType, act, type, power;
    for (const string &p : params)
    {
        size_t eq_pos = p.find('=');
        if (eq_pos != string::npos)
        {
            string key = p.substr(0, eq_pos);
            string value = p.substr(eq_pos + 1);

            // 转换为大写进行比较
            transform(key.begin(), key.end(), key.begin(), ::toupper);
            transform(value.begin(), value.end(), value.begin(), ::toupper);

            if (key == "MSGTYPE")
                msgType = value;
            else if (key == "ACT")
                act = value;
            else if (key == "TYPE")
                type = value;
            else if (key == "POWER")
                power = value;
        }
    }

    // 验证消息格式
    if (msgType != "CONTROL" || act != "SENSOR")
    {
        MOOSTrace("[LEAK] 无效的控制消息格式: %s\n", msg.c_str());
        return;
    }

    // 根据设备类型和电源状态设置控制数组（参考iCAN0_HEU逻辑）
    bool powerOn = (power == "ON");

    if (type == "DVL")
    {
        m_device_control[5] = powerOn ? 0x01 : 0x00;
    }
    else if (type == "USBL")
    {
        m_device_control[3] = powerOn ? 0x01 : 0x00;
    }
    else if (type == "LOAD")
    {
        m_device_control[6] = powerOn ? 0x02 : 0x00;
    }
    else if (type == "LED")
    {
        m_device_control[4] = powerOn ? 0x02 : 0x00;
    }
    else if (type == "HYP")
    {
        m_device_control[2] = powerOn ? 0x02 : 0x00;
    }
    else if (type == "AVOID")
    {
        m_device_control[7] = powerOn ? 0x01 : 0x00; // 避障声呐
    }
    else if (type == "AEMLOADHIGH")
    {
        m_device_control[8] = powerOn ? 0x01 : 0x00; // 载荷高压
    }
    else if (type == "AEMLOADLOW")
    {
        m_device_control[9] = powerOn ? 0x01 : 0x00; // 载荷低压
    }
    else if (type == "ALL")
    {
        // 所有设备控制
        if (powerOn)
        {
            m_device_control[1] = 0x01; // PPB
            m_device_control[2] = 0x02; // HYP
            m_device_control[3] = 0x01; // USBL
            m_device_control[4] = 0x02; // LED
            m_device_control[5] = 0x01; // DVL
            m_device_control[6] = 0x00; // LOAD保持关闭
            m_device_control[7] = 0x01; // AVOID
            m_device_control[8] = 0x01; // AEMLOADHIGH
            m_device_control[9] = 0x01; // AEMLOADLOW
        }
        else
        {
            for (int i = 1; i < 10; i++)
            {
                m_device_control[i] = 0x00;
            }
        }
    }
    else
    {
        MOOSTrace("[LEAK] 未知的设备类型: %s\n", type.c_str());
        return;
    }

    m_control_frame_ready = true;
    MOOSTrace("[LEAK] 设备控制: %s %s\n", type.c_str(), powerOn ? "开启" : "关闭");

    // 立即发送控制帧
    SendControlFrame();
}

// 发送设备控制帧 (CAN ID: 0x321)
void iLEAK::SendControlFrame()
{
    if (!m_control_frame_ready)
    {
        return;
    }

    // 构造13字节网络帧：[08 00 00 03 21] + [FA + 控制数据 + FA]
    // 参考iCAN0_HEU的数据组合方式
    vector<uint8_t> control_frame = {
        0x08, 0x00, 0x00, 0x03, 0x21,                                    // 网络帧头 + CAN ID 0x321
        0xFA,                                                            // 控制帧标识符
        static_cast<uint8_t>(m_device_control[1] | m_device_control[2]), // COM4控制 (PPB|HYP)
        static_cast<uint8_t>(m_device_control[3] | m_device_control[4]), // COM2控制 (USBL|LED)
        static_cast<uint8_t>(m_device_control[5] | m_device_control[6]), // COM6控制 (DVL|LOAD)
        m_device_control[7],                                             // COM9控制 (AVOID)
        m_device_control[8],                                             // 载荷高压
        m_device_control[9],                                             // 载荷低压
        0xFA                                                             // 控制帧结束标识符
    };

    // 通过BlueSocket发送13字节网络帧到CAN网络
    SendFrame(control_frame);

    MOOSTrace("[LEAK] 设备控制帧发送: [0x08,0x00,0x00,0x03,0x21,0xFA,0x%02X,0x%02X,0x%02X,0x%02X,0x%02X,0x%02X,0xFA]\n",
              control_frame[6], control_frame[7], control_frame[8],
              control_frame[9], control_frame[10], control_frame[11]);

    m_control_frame_ready = false; // 重置发送标志
}

// 解析设备控制帧 (CAN ID: 0x321) - 用于接收其他模块发送的控制命令
void iLEAK::ParseControlFrame(vector<uint8_t> Frame)
{
    // 验证数据帧长度 (13字节网络帧)
    if (Frame.size() != FRAME_LEN)
    {
        MOOSTrace("[LEAK] Invalid control frame length: %d, expected: %d\n", Frame.size(), FRAME_LEN);
        return;
    }

    // 完整CAN ID = (Frame[1]<<24) | (Frame[2]<<16) | (Frame[3]<<8) | Frame[4]
    uint32_t received_can_id = (Frame[1] << 24) | (Frame[2] << 16) | (Frame[3] << 8) | Frame[4];
    if (received_can_id != 0x321)
    {
        MOOSTrace("[LEAK] Invalid control CAN ID: 0x%08X, expected: 0x321\n", received_can_id);
        return;
    }

    // 检查控制帧标识符 (第6字节和第13字节应为0xFA)
    if (Frame[5] != 0xFA || Frame[12] != 0xFA)
    {
        MOOSTrace("[LEAK] Invalid control frame identifier: start=0x%02X, end=0x%02X, expected: 0xFA\n",
                  Frame[5], Frame[12]);
        return;
    }

    // 提取控制数据 (字节6-11)
    uint8_t com4_control = Frame[6]; // COM4控制 (PPB|HYP)
    uint8_t com2_control = Frame[7]; // COM2控制 (USBL|LED)
    uint8_t com6_control = Frame[8]; // COM6控制 (DVL|LOAD)
    uint8_t com9_control = Frame[9]; // COM9控制 (AVOID)
    uint8_t high_power = Frame[10];  // 载荷高压
    uint8_t low_power = Frame[11];   // 载荷低压

    MOOSTrace("[LEAK] 接收到设备控制帧: COM4=0x%02X, COM2=0x%02X, COM6=0x%02X, COM9=0x%02X, HIGH=0x%02X, LOW=0x%02X\n",
              com4_control, com2_control, com6_control, com9_control, high_power, low_power);

    // 解析抛载控制状态并发布MOOS变量
    bool load_status = (com6_control & 0x02) != 0; // 检查LOAD位
    Notify("LOAD_STATUS", load_status);

    // 可以根据需要发布其他设备状态
    Notify("DEVICE_CONTROL_RECEIVED", true);
}

//---------------------------------------------------------
// Procedure: OnStartUp()
//            happens before connection is open
