/************************************************************/
/*    NAME: PengYiyao                                       */
/*    ORGN: MIT                                             */
/*    FILE: main.cpp                                        */
/*    DATE:                                                 */
/************************************************************/

#include <string>
#include "MBUtils.h"
#include "ColorParse.h"
#include "CAN_HEU.h"
#include "CAN_HEU_Info.h"
#include "MOOS/libMOOS/MOOSLib.h"

using namespace std;

int main(int argc, char *argv[])
{
 // string mission_file;
  /*string run_command = argv[0];

  for(int i=1; i<argc; i++) {
    string argi = argv[i];
    if((argi=="-v") || (argi=="--version") || (argi=="-version"))
      showReleaseInfoAndExit();
    else if((argi=="-e") || (argi=="--example") || (argi=="-example"))
      showExampleConfigAndExit();
    else if((argi == "-h") || (argi == "--help") || (argi=="-help"))
      showHelpAndExit();
    else if((argi == "-i") || (argi == "--interface"))
      showInterfaceAndExit();
    else if(strEnds(argi, ".moos") || strEnds(argi, ".moos++"))
      mission_file = argv[i];
    else if(strBegins(argi, "--alias="))
      run_command = argi.substr(8);
    else if(i==2)
      run_command = argi;
  }
  
  if(mission_file == "")
    showHelpAndExit();

  cout << termColor("green");
  cout << "iCAN_HEU launching as " << run_command << endl;
  cout << termColor() << endl;*/
  const char * sMissionFile = "iCAN_HEU.moos";
  if(argc>1)
    {
        sMissionFile = argv[1];
    }

  CAN_HEU CAN_HEU;
  
  //CAN_HEU.Run(run_command.c_str(), mission_file.c_str());
    CAN_HEU.Run("iCAN0_HEU",sMissionFile);
  return 0;
}

