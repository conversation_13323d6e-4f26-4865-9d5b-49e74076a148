{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "c:/Users/<USER>/Desktop/bb/ILEAK/漏水32的代码/2023-12-19/Core/Src", "program": "c:/Users/<USER>/Desktop/bb/ILEAK/漏水32的代码/2023-12-19/Core/Src/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}