/************************************************************/
/*    NAME: PengYiyao      fl                                 */
/*    ORGN: MIT                                             */
/*    FILE: CAN_HEU.cpp                                     */
/*    DATE:  2023/4/3                                             */
/************************************************************/
#include <iostream>
#include <iterator>
#include "MBUtils.h"
#include "CAN_HEU.h"
#include "CAN.c"
#include"rs232.c"
#include"rs232.h"
#include <linux/can.h>
#include <linux/can/raw.h>
#include <string>
using namespace std;

//---------------------------------------------------------
// Constructor

CAN_HEU::CAN_HEU()
{
  SetAppFreq(10);
  SetCommsFreq(10);
  m_iterations = 0;
  m_timewarp   = 1;

  m_sLEAK = "NONE";
  m_dfLEAK = 0;
  m_cLEAK[20] = {'0'};
  m_dfLEAK2=0;
  istrue=false;
/*******************************/

  /*this is for can*/     
  /*------- register handler on SIGINT signal -------*/
  act.sa_handler=sortie;
  sigemptyset(&act.sa_mask);
  act.sa_flags=0;
  sigaction(SIGINT,&act,0);
  /*---------------------------------------*/	        
 

  //rfilter.can_id = 0x11;  200// rfilter.can_mask = CAN_SFF_MASK;	//.can_mask = 0x1fffffff,
	
  int family = PF_CAN, type = SOCK_RAW, proto = CAN_RAW;
  nbytes=0; dlc = 0; rtr = 1; extended = 0; count = 0;filter = 1;
  bitrate=0; mode=0; opt=0;restart_ms=0;
  opterr = 0;
  int port=0;
	restart_ms=100; //100ms
	bitrate = 250; //250kbit/s
    set_restart_bitrate(port,restart_ms,bitrate);
	s =open_can(port, family, type, proto, bitrate);
    set_filter(s,rfilter);
    if (rtr)
    {
        frame.can_id |= CAN_RTR_FLAG;
        frame.can_dlc = 8; /* data length code: 0 .. 8 */
        buf.can_id = 0x12;//帧ID
        buf.can_dlc = 8;
    }

    if (extended) 
    {
        buf.can_id &= CAN_EFF_MASK;
        buf.can_id |= CAN_EFF_FLAG;
	 	frame.can_id &= CAN_EFF_MASK;
	 	frame.can_id |= CAN_EFF_FLAG;
	} 
    else 
    {
	 	frame.can_id &= CAN_SFF_MASK;
	}



 /*this is for can*/
}

//---------------------------------------------------------
// Destructor

CAN_HEU::~CAN_HEU()
{

}

//---------------------------------------------------------
// Procedure: OnNewMail

bool CAN_HEU::OnNewMail(MOOSMSG_LIST &NewMail)
{
   UpdateLocalMOOSVariables(NewMail);
   CMOOSVariable *p = NULL;
   p = GetMOOSVar("HEU_SHORE_CONTROL_MESSAGE");
   if (p->IsFresh())
   {
        std::string paMsg = (*p).GetStringVal();
        HandleConfiguration_MSG(paMsg);
		istrue=true;
        p->SetFresh(false);
    }
  
   p = GetMOOSVar("LEAK2");
   if (p->IsFresh())
   {
        m_dfLEAK2 = (*p).GetDoubleVal();
        //m_dfLEAK2=atof(s_LEAK2.c_str());
        //MOOSTrace("m_dfLEAK2%f\n",m_dfLEAK2);
        p->SetFresh(false);
    }
   return(true);
}

//---------------------------------------------------------
// Procedure: OnConnectToServer

bool CAN_HEU::OnConnectToServer()
{
   RegisterVariables();
   return(true);
}
//---------------------------------------------------------
// Procedure: Iterate()
bool CAN_HEU::Iterate()
{
   m_iterations++;
  //MOOSTrace("This is Iterate\n");
  
  // double t = MOOSTime() ;
  /*set buf*/
   string str = m_cLEAK;
  // SetMOOSVar("LEAK",str,t);
  // PublishFreshMOOSVariables(); 
      /* unsigned char sendbuff[1];
         sendbuff[0]=0xFF;
        nbytes = write(s, &sendbuff, sizeof(sendbuff));*/
      /* MOOSTrace("This is Test Nbytes= %lf \n",nbytes);
        if(istrue)
        {
        if ((nbytes =  write(s,  &buf, sizeof(buf)))<0) 
         {
     MOOSTrace("nbytes = %d\n",nbytes);
     printf("can send is failed \n");                               
   }
 else{
 MOOSTrace("nbytes = %d\n",nbytes);
   MOOSTrace("XUNHUAN S= %d\n",s);
  printf("can send is succeed \n");
 }
MOOSTrace("it is true \n",nbytes);

  sleep(1);
 }*/
  if ((nbytes = read(s, &frame, sizeof(struct can_frame))) < 0) 
  {
       return(false);                           
   }
				
  else //if ((nbytes = read(s, &frame, sizeof(struct can_frame))) >= 0)
  {
      count++;
     if ((frame.data[0] == 0XFB)&&(frame.can_id == 0x532))//zhentou 
          {     //cout << "漏水检测开启啦" << endl;
                m_dfLEAK=frame.data[7];
                cout << m_dfLEAK<< endl;
         
                m_dfLEAK=m_dfLEAK+m_dfLEAK2;
                Notify("LEAK",m_dfLEAK);
              //  sprintf(m_cLEAK,"Leak=%f",m_dfLEAK);
                //m_sLEAK=(char*)frame.data[7];
	        //m_sLEAK=to_string(m_dfLEAK);
		//cout << m_cLEAK<< endl;
	  }	
   } 
 
  return(true);
}

//---------------------------------------------------------
// Procedure: OnStartUp()
//            happens before connection is   open

bool CAN_HEU::OnStartUp()
{
      list<string> sParams;
      CMOOSApp::OnStartUp();
      double df_CAN = 0.5;
      AddMOOSVariable("HEU_SHORE_CONTROL_MESSAGE",    "CONTROL_MSG",  "",    df_CAN);
     // AddMOOSVariable("LEAK",    "",  "LEAK",    df_CAN); //发布
      AddMOOSVariable("LEAK2",    "LEAK2",  "",    df_CAN); //订阅
      m_timewarp = GetMOOSTimeWarp();
//5.10
    string sSerialPort;
    char Serial[48];

    if (m_MissionReader.GetConfigurationParam("SerialPort", sSerialPort))
    {
        memcpy(Serial, sSerialPort.c_str(), sSerialPort.size());

        Serial[sSerialPort.size()] = '\0';
        MOOSTrace("SerialPort = %s\n", sSerialPort.c_str());
    }
    else
    {
        MOOSTrace("Getting SerialPort Failed\n");
       
    }

    string sBaudRate;
    long nBaudRate = 0;
//dvlSHANG
    if (m_MissionReader.GetConfigurationParam("BaudRate", sBaudRate))
    {
        nBaudRate = (long int)(atof(sBaudRate.c_str()));
        MOOSTrace("BaudRate = %ld\n", nBaudRate);
    }
    else
    {
        MOOSTrace("Getting BaudRate failed\n");
       
    }
         

    if (OpenComport(Serial, nBaudRate))
    {
        MOOSTrace("Opening SerialPort failed\n");
    
    }

    
      RegisterVariables();
      return(true);
}

//---------------------------------------------------------
// Procedure: RegisterVariables

void CAN_HEU::RegisterVariables()
{
      RegisterMOOSVariables();
}

bool CAN_HEU::UpdateLocalMOOSVariables(MOOSMSG_LIST &NewMail)
{

    MOOSVARMAP::iterator p;
    for(p = m_MOOSVars.begin();p!=m_MOOSVars.end();p++)
    {
        CMOOSVariable & rVar = p->second;
        CMOOSMsg Msg;
        //MOOSTrace("rVar.GetSubscribeName() is %s\n", rVar.GetSubscribeName().c_str());
        if(m_Comms.PeekMail(NewMail,rVar.GetSubscribeName(),Msg, false, true))
        {
      
                rVar.Set(Msg);
                rVar.SetFresh(true);
        }
    }
    return (true);
}

bool CAN_HEU::HandleConfiguration_MSG(std::string pam)
    {
        vector<string> pav;
        std::string sStr = pam;
        std::string sOneParam;
        vector<string> pyy;
      	MOOSTrace("sStr=%s\n",sStr.c_str());
	
        while (sStr != "")
        {
            // 按分号分割
            sOneParam = MOOSChomp(sStr, ";");
            if (sOneParam != "")
            {
                 // 存储每个参数
                pav.push_back(sOneParam);
                // 结果：pav = ["MsgType=control", "Act=sensor", "Type=LOAD", "Power=on"]
            }
        }
        int vsize = pav.size();
        if (vsize == 0)
        {
            return(false);
        }
        for (int i = 0; i < vsize ; i++)
        {
            string str = pav[i];//uPokeDB CONTROL_MSG="MsgType=control; Act=sensor; Type=ALL;Power=off;"
            string MSG_name = MOOSChomp(str, "="); //
            //cout << "str=" << str << endl;
            MOOSTrimWhiteSpace(MSG_name);//剪切空白区域
            MOOSToUpper(MSG_name);//将字符串转换为大写
            cout << MSG_name << endl;//Type mode dist radius
            std::string Act_name = str; // "control"
            MOOSTrimWhiteSpace(Act_name); // 去除空格
            MOOSToUpper(Act_name); // 转为大写
            cout << Act_name << endl;//mode=los;dist=10;radius=10
           pyy.push_back(Act_name);
           // 结果：pyy = ["CONTROL", "SENSOR", "LOAD", "ON"]
        }

       if (pyy[0] == "CONTROL") // 验证消息类型
        {
            frame.data[0] = 0XFC;
            if (pyy[1] == "SENSOR") // 验证消息类型
            {
                cout << "Act right" << endl;
                if (pyy[2] == "NOTHING")
                {
                    cout << "This is nothing" << endl;
                    if (pyy[3] == "ON")
                    {
                        data1[0] = 0x01;
		        cout << "This is nothing ON" << endl;
                    }
                    else if (pyy[3] == "OFF")
                    {
                        data1[0] = 0x00;
		        cout << "This is nothing OFF" << endl;
                    }

                }

                else if (pyy[2] == "PPB")//吉大板卡
                {
                    cout << "This is PPB" << endl;
                    if (pyy[3] == "ON")
                    {
                        data1[1] = 0x01;
                    }
                    else if (pyy[3] == "OFF")
                    {
                        data1[1] = 0x00;
                    }

                }

                else if (pyy[2] == "HYP")//水声通讯
                {
                    cout << "This is HYP" << endl;
                    if (pyy[3] == "ON")
                    {
                        data1[2] = 0x02;
                    }
                    else if (pyy[3] == "OFF")
                    {
                        data1[2] = 0x00;
                    }
                }

                else if (pyy[2] == "USBL")
                {
                    cout << "This is USBL" << endl;
                    if (pyy[3] == "ON")
                    {
                        data1[3] = 0x01;
                    }
                    else if (pyy[3] == "OFF")
                    {
                        data1[3] = 0x00;
                    }
                }

                else if (pyy[2] == "LED")//频闪灯
                {
                    cout << "This is LED" << endl;
                    if (pyy[3] == "ON")
                    {
                        data1[4] = 0x02;
                    }
                    else if (pyy[3] == "OFF")
                    {
                        data1[4] = 0x00;
                    }
                }

                else if (pyy[2] == "DVL")
                {
                    cout << "This is DVL" << endl;
                    if (pyy[3] == "ON")
                    {
                        data1[5] = 0x01;
                    }
                    else if (pyy[3] == "OFF")
                    {
                        data1[5]= 0x00;
                    }
                }

                else if (pyy[2] == "LOAD")  // 验证消息类型
                {
                    cout << "This is LOAD" << endl;
                    if (pyy[3] == "ON")
                    {
                        data1[6] = 0x02;
                    }
                    else if (pyy[3] == "OFF")
                    {
                        data1[6] = 0x00;
                    }
                }
                else if (pyy[2] == "ALL")
                {
                    cout << "This is ALL" << endl;
                    if (pyy[3] == "ON")
                    {
                        data1[0]= 0X00;
                        data1[1]= 0X01;
                        data1[2]= 0X02;
                        data1[3]= 0X01;
             		    data1[4]= 0X02;
                        data1[5]= 0X01;
 			            data1[6]= 0X00;
                        data1[7]= 0X00;
 			            data1[8]= 0X00;
                        data1[9]= 0X00;
                    }
                    else if (pyy[3] == "OFF")
                    {
                        data1[0]= 0X00;
                        data1[1]= 0X00;
                        data1[2]= 0X00;
                        data1[3]= 0X00;
             		    data1[4]= 0X00;
                        data1[5]= 0X00;
 			            data1[6]= 0X00;
                        data1[7]= 0X00;
 			            data1[8]= 0X00;
                        data1[9]= 0X00;
                    }
                    
                }
            }
            buf.data[0] = 0XFC;
            buf.data[1] = data1[1] | data1[2];
            buf.data[2] = data1[3] | data1[4];
            buf.data[3] = data1[5] | data1[6];
            buf.data[4] = data1[7];
            buf.data[5] = 0x00;
            buf.data[6] = 0XFC;
            buf.data[7] = 0XFC;
	for(int i=0;i<8;i++)
	{
            MOOSTrace("buf = %02x\n",buf.data[i]);
	}
        nbytes = write(s, &buf, sizeof(buf));
        // MOOSTrace("nbytes = %d\n",nbytes);
         usleep(20000);  //挂机0.02s
        //int cport=backsocket();
	 //MOOSTrace("Cport=%d\n",cport);
  
         //printf("data = %02x\n",frame.data[2])
        }
 
    }


