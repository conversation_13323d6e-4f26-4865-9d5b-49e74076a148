/************************************************************/
/*    NAME: zhaoqinchao                                    */
/*    ORGN: HEU                                             */
/*    FILE: BlueSocket.h                                    */
/*    DATE: 2025/07/18                                      */
/*    VERSION: 2.0                                          */
/*    CHANGES:                                              */
/*      - 添加容错机制支持 (OpenSocketWithRetry)            */
/*      - 添加套接字重绑定功能 (RebindSocket)               */
/*      - 增强二进制数据传输支持                            */
/*      - 添加绑定状态标志 (m_bBindFlag)                    */
/*      - 优化网络连接稳定性和可靠性                        */
/************************************************************/

#ifndef _BLUESOCKET_H_
#define _BLUESOCKET_H_

#include <stdio.h>
#include <errno.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <string>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <vector>
#include <stdint.h>

class BlueSocket
{
public:
    BlueSocket();
    virtual ~BlueSocket();

protected:
    int m_iSockfd;
    struct sockaddr_in m_SockAddr;
    bool m_bBindFlag;  // 绑定状态标志

public:
    bool OpenSocket(const std::string &sIP, const int &iPort);
    bool OpenSocketWithRetry(const std::string &sIP, const int &iPort,
                            const int maxRetries = 5, const int retryDelay = 2);
    bool SetNonBlocking();
    bool BindSocket();
    bool RebindSocket(const int maxRetries = 5, const int retryDelay = 2);
    int SendString(const std::string &data);
    int SendBinary(const std::vector<uint8_t> &data);
    int RecvString(std::string &data, const int &iBufferSize);
    int RecvBinary(std::vector<uint8_t> &data, const int &iBufferSize);
};

#endif
